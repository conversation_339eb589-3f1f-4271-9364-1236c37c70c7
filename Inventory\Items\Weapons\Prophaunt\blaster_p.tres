[gd_resource type="Resource" script_class="GunInventoryItem" load_steps=4 format=3 uid="uid://iwu5r0v2c3os5"]

[ext_resource type="Texture2D" uid="uid://pm05jfwruxpr" path="res://Scenes/ui/assets/pistol.png" id="1_blaster_p_icon"]
[ext_resource type="PackedScene" uid="uid://lb4ki4hp4c7y" path="res://prophaunt/assets/Guns/Scene/blaster_p.tscn" id="2_blaster_p_scene"]
[ext_resource type="Script" path="res://Inventory/GunInventoryItem.gd" id="3_blaster_p_script"]

[resource]
script = ExtResource("3_blaster_p_script")
damage = 80
ammo = 25
gun_type = "range"
range = 35
animation_speed = 3.5
scene = ExtResource("2_blaster_p_scene")
scale = Vector3(150, 150, 150)
position = Vector3(0, 0, 0)
rotation = Vector3(10, 125, 90)
ui = 2
id = 108
name = "BLASTER_P"
icon = ExtResource("1_blaster_p_icon")
price = 250
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
