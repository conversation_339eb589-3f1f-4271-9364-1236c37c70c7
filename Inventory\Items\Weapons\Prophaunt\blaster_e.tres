[gd_resource type="Resource" script_class="GunInventoryItem" load_steps=4 format=3 uid="uid://esq1n6r8y9ko1"]

[ext_resource type="Texture2D" uid="uid://pm05jfwruxpr" path="res://Scenes/ui/assets/pistol.png" id="1_blaster_e_icon"]
[ext_resource type="PackedScene" uid="uid://b28uyp054815m" path="res://prophaunt/assets/Guns/Scene/blaster_e.tscn" id="2_blaster_e_scene"]
[ext_resource type="Script" path="res://Inventory/GunInventoryItem.gd" id="3_blaster_e_script"]

[resource]
script = ExtResource("3_blaster_e_script")
damage = 95
ammo = 12
gun_type = "range"
range = 50
animation_speed = 2.8
scene = ExtResource("2_blaster_e_scene")
scale = Vector3(150, 150, 150)
position = Vector3(0, 0, 0)
rotation = Vector3(10, 125, 90)
ui = 2
id = 104
name = "BLASTER_E"
icon = ExtResource("1_blaster_e_icon")
price = 350
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
