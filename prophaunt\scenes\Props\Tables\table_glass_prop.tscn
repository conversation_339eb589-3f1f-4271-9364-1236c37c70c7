[gd_scene load_steps=4 format=3 uid="uid://crqhtj6u4of0i"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_fbpi3"]
[ext_resource type="PackedScene" uid="uid://brqd6kjy72spu" path="res://prophaunt/maps/Source/Table/table_glass.tscn" id="2_c1pc8"]

[sub_resource type="BoxShape3D" id="BoxShape3D_chuyu"]
size = Vector3(0.845215, 0.332703, 0.447266)

[node name="TableGlassProp" instance=ExtResource("1_fbpi3")]

[node name="TableGlass" parent="Meshes" index="0" instance=ExtResource("2_c1pc8")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.000732422, 0.162262, 0.000976563)
shape = SubResource("BoxShape3D_chuyu")
