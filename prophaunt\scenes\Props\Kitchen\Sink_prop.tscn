[gd_scene load_steps=5 format=3 uid="uid://6t50sexrfrl4"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_vnvds"]
[ext_resource type="PackedScene" uid="uid://qmw5r63ak48d" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/Sink.tscn" id="2_l3oqr"]
[ext_resource type="PackedScene" uid="uid://c4a2qxy83e3ob" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/DishrackPlate.tscn" id="2_vns2y"]

[sub_resource type="BoxShape3D" id="BoxShape3D_auoo1"]
size = Vector3(2.95435, 2.1593, 2.03333)

[node name="SinkProp" instance=ExtResource("1_vnvds")]

[node name="DishrackPlate" parent="Meshes" index="0" instance=ExtResource("2_vns2y")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.776082, 1.09411, 0)

[node name="Sink" parent="Meshes/DishrackPlate" index="4" instance=ExtResource("2_l3oqr")]
transform = Transform3D(1, 0, 0, 0, 0, -1, 0, 1, 0, 0.776082, -1.09411, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0460205, 1.09364, 0.00299072)
shape = SubResource("BoxShape3D_auoo1")
