[gd_resource type="Resource" script_class="GunInventoryItem" load_steps=4 format=3 uid="uid://bqm8k3n5v6hl8"]

[ext_resource type="Texture2D" uid="uid://pm05jfwruxpr" path="res://Scenes/ui/assets/pistol.png" id="1_m4_icon"]
[ext_resource type="PackedScene" uid="uid://wpaglji3gno6" path="res://prophaunt/assets/Guns/Scene/M4.tscn" id="2_m4_scene"]
[ext_resource type="Script" path="res://Inventory/GunInventoryItem.gd" id="3_m4_script"]

[resource]
script = ExtResource("3_m4_script")
damage = 120
ammo = 30
gun_type = "range"
range = 60
animation_speed = 2.5
scene = ExtResource("2_m4_scene")
scale = Vector3(150, 150, 150)
position = Vector3(0, 0, 0)
rotation = Vector3(10, 125, 90)
ui = 2
id = 101
name = "M4"
icon = ExtResource("1_m4_icon")
price = 500
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
