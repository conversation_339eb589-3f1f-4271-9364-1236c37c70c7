[gd_scene load_steps=3 format=4 uid="uid://gr2gg76rbykx"]

[sub_resource type="ArrayMesh" id="ArrayMesh_o8g2f"]
_surfaces = [{
"aabb": AABB(-3.19491, -1, -0.187558, 6.38981, 2, 0.346367),
"format": 34359742465,
"index_count": 120,
"index_data": PackedByteArray("AAADAAEAAAACAAMAAgAIAAMAAgAJAAgABgAFAAcABgAEAAUAAgAEAAYAAgAAAAQABwABAAMABwAFAAEABwACAAYABwAJAAIACAAHAAMACQAHAAgAEwANABUAEwAMAA0AFQALABQAFQANAAsAEgAMABMAEgAKAAwAFAAKABIAFAALAAoAAQAQAAAAAQAOABAAAAARAAQAAAAQABEABQAOAAEABQAPAA4ABAAPAAUABAARAA8ADgASABAADgAUABIAEAATABEAEAASABMADwAUAA4ADwAVABQAEQAVAA8AEQATABUAGQAXABgAGQAWABcA"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 26,
"vertex_data": PackedByteArray("U3lMwP7/f78lXqA9U3lMwP7/fz8lXqA9UnlMwP3/f79FD0C+UnlMwP3/fz9FD0C+U3lMQP7/f78lXqA9U3lMQP7/fz8lXqA9UnlMQP3/f79FD0C+UnlMQP3/fz9FD0C+il05wMvVMD9FD0C+il05wMvVML9FD0C+Yb07wAOsOr/g/T+6Yb07wAOsOj9g2z+6Yb07QAOsOr/g/T+6Yb07QAOsOj/g/T+6dkNKwEHKdj/EniI+dkNKQEHKdj/EniI+dkNKwEHKdr/EniI+dkNKQEHKdr/EniI+aNdAwEKgTr8QUR0+aNdAQEKgTr8QUR0+aNdAwEKgTj8OUR0+aNdAQEKgTj8OUR0+O8I8wH6BRb9QByU8O8I8wEEjQz9QByU8O8I8QEEjQz9QByU8O8I8QH6BRb9QByU8")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_ue7mc"]
resource_name = "WallDoorwaySign_WallDoorwaySign"
_surfaces = [{
"aabb": AABB(-3.19491, -1, -0.187558, 6.38981, 2, 0.346367),
"attribute_data": PackedByteArray("IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8de5M+cM88Px17kz5wzzw/HXuTPnDPPD8de5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD8he5M+cM88PyF7kz5wzzw/IXuTPnDPPD9c3l4+oEmYPlzeXj6gSZg+XN5ePqBJmD5c3l4+oEmYPg=="),
"format": 34359742487,
"index_count": 120,
"index_data": PackedByteArray("AwANAAcAAwAKAA0ACAAcAAsACAAdABwAGAAVABsAGAARABUACQAPABcACQABAA8AGgAFAAwAGgATAAUAGQAIABYAGQAdAAgAHAAZAAsAHQAZABwAPQAlAEUAPQAjACUAQwAgAD8AQwAkACAANwAiADsANwAeACIAQQAfADkAQQAhAB8ABgAxAAIABgApADEAAAAzAA4AAAAvADMAEgAmAAQAEgAqACYAEAAsABQAEAA0ACwAKAA4ADAAKABAADgALgA6ADIALgA2ADoAKwA+ACcAKwBCAD4ANQBEAC0ANQA8AEQASQBHAEgASQBGAEcA"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 74,
"vertex_data": PackedByteArray("U3lMwP7/f78lXqA9U3lMwP7/f78lXqA9U3lMwP7/f78lXqA9U3lMwP7/f78lXqA9U3lMwP7/fz8lXqA9U3lMwP7/fz8lXqA9U3lMwP7/fz8lXqA9U3lMwP7/fz8lXqA9UnlMwP3/f79FD0C+UnlMwP3/f79FD0C+UnlMwP3/f79FD0C+UnlMwP3/fz9FD0C+UnlMwP3/fz9FD0C+UnlMwP3/fz9FD0C+U3lMQP7/f78lXqA9U3lMQP7/f78lXqA9U3lMQP7/f78lXqA9U3lMQP7/f78lXqA9U3lMQP7/fz8lXqA9U3lMQP7/fz8lXqA9U3lMQP7/fz8lXqA9U3lMQP7/fz8lXqA9UnlMQP3/f79FD0C+UnlMQP3/f79FD0C+UnlMQP3/f79FD0C+UnlMQP3/fz9FD0C+UnlMQP3/fz9FD0C+UnlMQP3/fz9FD0C+il05wMvVMD9FD0C+il05wMvVML9FD0C+Yb07wAOsOr/g/T+6Yb07wAOsOr/g/T+6Yb07wAOsOj9g2z+6Yb07wAOsOj9g2z+6Yb07QAOsOr/g/T+6Yb07QAOsOr/g/T+6Yb07QAOsOj/g/T+6Yb07QAOsOj/g/T+6dkNKwEHKdj/EniI+dkNKwEHKdj/EniI+dkNKwEHKdj/EniI+dkNKwEHKdj/EniI+dkNKQEHKdj/EniI+dkNKQEHKdj/EniI+dkNKQEHKdj/EniI+dkNKQEHKdj/EniI+dkNKwEHKdr/EniI+dkNKwEHKdr/EniI+dkNKwEHKdr/EniI+dkNKwEHKdr/EniI+dkNKQEHKdr/EniI+dkNKQEHKdr/EniI+dkNKQEHKdr/EniI+dkNKQEHKdr/EniI+aNdAwEKgTr8QUR0+aNdAwEKgTr8QUR0+aNdAwEKgTr8QUR0+aNdAwEKgTr8QUR0+aNdAQEKgTr8QUR0+aNdAQEKgTr8QUR0+aNdAQEKgTr8QUR0+aNdAQEKgTr8QUR0+aNdAwEKgTj8OUR0+aNdAwEKgTj8OUR0+aNdAwEKgTj8OUR0+aNdAwEKgTj8OUR0+aNdAQEKgTj8OUR0+aNdAQEKgTj8OUR0+aNdAQEKgTj8OUR0+aNdAQEKgTj8OUR0+O8I8wH6BRb9QByU8O8I8wEEjQz9QByU8O8I8QEEjQz9QByU8O8I8QH6BRb9QByU8/3+IJ////z//fwAA////v20m/3////8/AAD/f////7//f3bY////P/9///////+/bSb/f////z8AAP9/////v/////////+//38AAP///78AAP9/////v/////////+//3///////78AAP9/////v/9/iCf///8//38AAP///7+R2f9/////P////3////+//3922P///z//f///////v5HZ/3////8/////f////7//////////v/9/AAD///+/////f////7//////////v/9///////+/////f////7//////////v/////////+//38N1f///z9o1P9/////P/9/8Sr///8/aNT/f////z//fw3V////P5Yr/3////8//3/xKv///z+WK/9/////P/9/dtj///8//3/oe////z9ahP9/////P20m/3////8//3922P///z//f+h7////P5HZ/3////8/pHv/f////z//fxaE////P/9/iCf///8/WoT/f////z9tJv9/////P/9/FoT///8//3+IJ////z+R2f9/////P6R7/3////8//38WhP///z//fw3V////P1qE/3////8/aNT/f////z//fxaE////P/9/DdX///8/pHv/f////z+WK/9/////P/9/6Hv///8//3/xKv///z9ahP9/////P2jU/3////8//3/oe////z//f/Eq////P6R7/3////8/liv/f////z//f/9/////P/9//3////8//3//f////z//f/9/////Pw==")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_o8g2f")

[node name="WallDoorwaySign" type="MeshInstance3D"]
transform = Transform3D(0.272165, 0, 0, 0, 0.276767, 0, 0, 0, 0.272165, 0, 3.55367, -1.78921)
mesh = SubResource("ArrayMesh_ue7mc")
skeleton = NodePath("")
