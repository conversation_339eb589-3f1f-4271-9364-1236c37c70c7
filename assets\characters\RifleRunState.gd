class_name RifleRunState
extends Node

@onready var character: Character = $"../.."

func _ready() -> void:
	pass

func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return

	# Check for reload input while running
	if Input.is_action_just_pressed("reload"):
		character.state = character.State.RIFLE_RELOAD_RUN
		return

	# Check for shoot input - transition to shoot_run since we're running
	if Input.is_action_just_pressed("shoot"):
		character.state = character.State.RIFLE_SHOOT_RUN
		return

	# Check for switching back to normal states (e.g., when weapon is holstered)
	if Input.is_action_just_pressed("holster_weapon"):
		character.state = character.State.STATE_RUN
		return

	# Movement and jumping are handled in handle_rifle_mode
	character.handle_rifle_mode(delta, true)

func start_state():
	if character.state == character.State.RIFLE_RUN:
		return
	
	character.state = character.State.RIFLE_RUN
	# Animation will be handled in handle_animation function


func end_state():
	# Clean up when leaving this state
	pass
