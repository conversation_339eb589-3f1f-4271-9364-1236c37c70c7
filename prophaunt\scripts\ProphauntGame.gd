extends Control
class_name Prop<PERSON>aunt<PERSON><PERSON>

# Prophaunt client-side game controller
# Manages the client-side game state and UI

@onready var player_parent: Node3D = $Player
@onready var map_http_request: HTTPRequest = $MapHTTPRequest
@onready var client_request: HTTPRequest = $ClientRequest


# Game UI
@onready var game_ui: ProphauntGameUI = $ProphauntGameUI
@onready var find_game_hud: Control = $FindGameHUD
@onready var find_game_loading: Panel = $FindGameHUD/Loading
@onready var find_game_found_panel: Panel = $FindGameHUD/FindGame
@onready var server_check_label: Label = $FindGameHUD/Loading/ServerCheckLabel
@onready var waiting_label: Label = $FindGameHUD/Loading/WaitingLabel
@onready var touch_controller: TouchController = $TouchController
@onready var camera_controller: Node3D = $CameraController


var server
var client
var server_node = preload("res://networking/server.tscn")
var client_node = preload("res://networking/client.tscn")
var selected_map_packed
var selected_map

# Game state
var local_player_id: int = -1
var current_team: Constants.ProphauntTeam
var game_started: bool = false
var connected_to_server: bool = false

# Player management
var prophaunt_player_manager: ProphauntPlayerManager

#Client
var server_index = 0 #For selecting which server to connect
var available_servers = [] #Result of all available servers
var player: Character


func _ready():
	# Initialize
	Selector.selected_game_mode = Constants.GameMode.Prophaunt
	server = server_node.instantiate()

	server.player_parent = player_parent
	add_child(server)
	server.game_scene = self
	Constants.server = server
	server.on_new_game()
	
	client = client_node.instantiate()
	client.game_scene = self
	ClientRPC.client = client
	client.connect_success.connect(on_client_connect_success)
	client.error_connecting.connect(on_client_error_connecting)
	client.disconnected.connect(on_client_disconnect)
	add_child(client)
	client.remote_parent = player_parent
	Constants.client = client
	client.server = server
	server.client = client
	
	if Constants.is_client():
		local_player_id = multiplayer.get_unique_id()
	else:
		init_server_mode()

	# Initialize player manager
	prophaunt_player_manager = ProphauntPlayerManager.get_instance()

	# Connect to server
	if Constants.is_client():
		connect_to_prophaunt_server()


func init_server_mode():
	print("PropHaunt game. init server mode")
	send_select_map_request()


func client_init():
	if not Constants.is_client():
		return
	
	if StageManager.current_stage > 1:
		return
	
	SoundManager.play_bg_music()
	
	
	find_game_hud.visible = true
	find_game_loading.visible = true
	find_game_found_panel.visible = false
	var url = Constants.BACKEND_URL + "/game/find_prophaunt_game/"
	var data = {}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	
	if len(available_servers) == 0:
		client_request.cancel_request()
		client_request.request(url, headers, HTTPClient.METHOD_POST, json)
	else:
		if server_index >= len(available_servers):
			print("no server can serve you change your net!")
			ClientBackendManager.send_server_test_fail_request()
			on_client_disconnect()
		else:
			check_next_server()


#Client
func check_next_server():
	find_game_hud.visible = false
	find_game_loading.visible = true
	
	waiting_label.visible = false
	server_check_label.visible = true
	server_check_label.text = tr("SERVER_CHECK") + "#" + str(server_index + 1)
	#Create player and add to scene
	if Constants.is_headless:
		#init_player_character_headless()#For bots in previous implementation
		pass
	else:
		pass
		#Do Nothing
		#init_player_character()
	var current_server = available_servers[server_index]
	Constants.URL = current_server["ip"]
	Constants.PORT = int(current_server["port"])
	#Constants.URL = "************"
	#Constants.PORT = 11019
	StageManager.total_stages = current_server["total_stage"]
	StageManager.is_finished = false
	ClientRPC.connect_to_server()
	server_index += 1


func connect_to_prophaunt_server():
	client_init()

	# Initialize game UI
	initialize_game_ui()


func initialize_game_ui():
	"""Initialize the game UI"""

	# Connect UI signals
	game_ui.ability_used.connect(_on_ability_used)
	game_ui.chat_message_sent.connect(_on_chat_message_sent)

	# Hide initial UI elements
	game_ui.visible = true


func client_load_map(resource):
	if Constants.is_headless:#ClientHeadless
		return
	
	selected_map = load(resource)
	selected_map = selected_map.instantiate()
	selected_map.game_scene = self
	server.map = selected_map
	add_child(selected_map)
	client.iam_ready()


func set_player_team(team: Constants.ProphauntTeam):
	"""Set the local player's team"""
	current_team = team

	if game_ui:
		game_ui.set_team(team)

	print("Local player assigned to team: ", "Props" if team == Constants.ProphauntTeam.PROPS else "Haunters")


func prophaunt_lobby_update(lobby_data: Dictionary):
	"""Handle lobby update from server"""
	var _players_count = lobby_data.get("j", 0)
	var _min_players = lobby_data.get("s", 2)
	var _timer = lobby_data.get("t", 0)


func prophaunt_round_start(round_number: int, duration: float):
	"""Handle round start"""
	if game_ui:
		game_ui.on_round_started(round_number, duration)
	find_game_hud.visible = false
	game_started = true

	if game_ui:
		game_ui.show_notification("PROPHAUNT GAME STARTED!", 3.0)


func prophaunt_round_end(winner_team: Constants.ProphauntTeam, _results: Dictionary):
	"""Handle round end"""
	if game_ui:
		game_ui.on_round_ended(winner_team)

	print("Round ended - Winner: ", "Props" if winner_team == Constants.ProphauntTeam.PROPS else "Haunters")


@rpc("authority", "call_local", "reliable")
func prophaunt_game_update(game_data: Dictionary):
	"""Handle game state update"""
	var round_timer = game_data.get("round_timer", 0.0)
	var props_alive = game_data.get("props_alive", 0)
	var total_props = game_data.get("total_props", 0)
	var current_round = game_data.get("current_round", 1)

	if game_ui:
		game_ui.update_round_info(current_round, round_timer)
		game_ui.update_props_info(props_alive, total_props)

	# Update local player health if prop
	var player_states = game_data.get("player_states", {})
	if local_player_id in player_states:
		var player_state = player_states[local_player_id]
		var hp = player_state.get("hp", 100)
		var max_hp = 100  # Could be dynamic

		if game_ui and current_team == Constants.ProphauntTeam.PROPS:
			game_ui.update_health(hp, max_hp)


@rpc("authority", "call_local", "reliable")
func prophaunt_player_eliminated(player_id: int):
	"""Handle player elimination"""
	if game_ui:
		game_ui.on_player_eliminated(player_id)

	print("Player ", player_id, " eliminated")


@rpc("authority", "call_local", "reliable")
func prophaunt_player_damaged(player_id: int, new_hp: int):
	"""Handle player damage"""
	if player_id == local_player_id and game_ui:
		game_ui.update_health(new_hp, 100)

	print("Player ", player_id, " damaged - HP: ", new_hp)


@rpc("authority", "call_local", "reliable")
func prophaunt_player_hexed(player_id: int, duration: float):
	"""Handle player hex"""
	if game_ui:
		var message = "Player hexed for " + str(duration) + " seconds!"
		game_ui.show_notification(message, 2.0)

	print("Player ", player_id, " hexed for ", duration, " seconds")


@rpc("authority", "call_local", "reliable")
func prophaunt_player_unhexed(player_id: int):
	"""Handle player unhex"""
	print("Player ", player_id, " unhexed")


@rpc("authority", "call_local", "reliable")
func prophaunt_player_disguised(player_id: int, disguise_index: int):
	"""Handle player disguise change"""
	var p:Character = server.players[player_id]
	var disguise = p.prophaunt_player.disguise_system.find_disguise_by_index(disguise_index)
	p.prophaunt_player.disguise_system.apply_disguise(disguise)


@rpc("authority", "call_local", "reliable")
func prophaunt_round_warning(warning_type: String, time_remaining: float):
	"""Handle round time warning"""
	if game_ui:
		game_ui.on_time_warning(warning_type)

	print("Round warning: ", warning_type, " - ", time_remaining, " seconds")


@rpc("authority", "call_local", "reliable")
func prophaunt_final_results(results: Dictionary):
	"""Handle final game results"""
	print("Final results: ", results)

	if game_ui:
		game_ui.show_notification("GAME ENDED - CHECK RESULTS!", 5.0)


@rpc("authority", "call_local", "reliable")
func prophaunt_game_ended():
	"""Handle game end"""
	print("Prophaunt game ended")

	if game_ui:
		game_ui.show_notification("GAME ENDED", 3.0)


# UI event handlers
func _on_ability_used(ability_index: int):
	"""Handle ability use from UI"""
	var prophaunt_player = prophaunt_player_manager.get_prophaunt_player(local_player_id)
	if not prophaunt_player:
		return

	match ability_index:
		0:  # First ability
			if current_team == Constants.ProphauntTeam.PROPS:
				# Disguise change
				var new_disguise = prophaunt_player.disguise_system.get_random_disguise()
				prophaunt_player.change_disguise(new_disguise)
			else:
				# Shoot
				var target_pos = get_aim_target()
				prophaunt_player.shoot_at_target(target_pos)
		1:  # Second ability
			if current_team == Constants.ProphauntTeam.PROPS:
				# Hex
				prophaunt_player.cast_hex()
			else:
				# Grenade
				var target_pos = get_aim_target()
				prophaunt_player.throw_grenade(target_pos)
		2:  # Third ability
			# Additional abilities can be added here
			pass


func _on_chat_message_sent(message: String):
	"""Handle chat message from UI"""
	# Send chat message to server
	print("Chat message: ", message)


func get_aim_target() -> Vector3:
	"""Get the current aim target position"""
	# This would typically use the camera and mouse position
	# For now, return a position in front of the player
	return Vector3(0, 0, -5)


func _on_back_button_pressed():
	"""Handle back button press"""
	SoundManager.play_click_sound()

	# Disconnect from server if connected
	if connected_to_server:
		# Disconnect logic here
		pass

	get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")


func _process(_delta):
	"""Update game controller"""
	if Input.is_action_just_pressed("exit"):
		_on_back_button_pressed()

	# Update UI cooldowns if game is active
	if game_started and game_ui and prophaunt_player_manager:
		update_ability_cooldowns()


func update_ability_cooldowns():
	"""Update ability cooldown displays"""
	var prophaunt_player = prophaunt_player_manager.get_prophaunt_player(local_player_id)
	if not prophaunt_player:
		return

	if current_team == Constants.ProphauntTeam.PROPS:
		# Prop abilities
		var disguise_cooldown = 0.0
		var hex_cooldown = prophaunt_player.hex_cooldown

		if prophaunt_player.disguise_system:
			disguise_cooldown = prophaunt_player.disguise_system.get_disguise_cooldown_remaining()

		game_ui.update_ability_cooldown(0, disguise_cooldown)
		game_ui.update_ability_cooldown(1, hex_cooldown)
		game_ui.update_ability_cooldown(2, 0.0)  # No third ability yet
	else:
		# Haunter abilities
		var gun_cooldown = 0.0
		var grenade_cooldown = 0.0

		if prophaunt_player.weapon_system:
			gun_cooldown = prophaunt_player.weapon_system.get_gun_cooldown_remaining()
			grenade_cooldown = prophaunt_player.weapon_system.get_grenade_cooldown_remaining()

		game_ui.update_ability_cooldown(0, gun_cooldown)
		game_ui.update_ability_cooldown(1, grenade_cooldown)
		game_ui.update_ability_cooldown(2, 0.0)  # No third ability yet


#Client
func on_client_connect_success():
	init_my_player_character()
	client.init_after_connect()


func init_my_player_character():
	var selected_player = load(Selector.selected_character["path"])
	print(Selector.selected_character)
	player = selected_player.instantiate()
	player.name = str(multiplayer.get_unique_id())
	player_parent.add_child(player)
	player.clientNetworkHistory.reset()
	player.make_player()
	player.set_camera(camera_controller)
	player.set_controller(touch_controller)
	player.set_character_name(Selector.my_name)
	player.client = client
	player.server = server
	client.set_my_player_scene(player, selected_player.resource_path, Selector.selected_character["id"])
	touch_controller.camera_controller = camera_controller
	touch_controller.client = client
	return player


#Server
func send_select_map_request():
	var url = Constants.BACKEND_URL + "/game/select_prophaunt_map/"
	var headers = ["Content-Type: application/json", "Authorization: JWT " + Constants.BACKEND_TOKEN]
	var data = {
		"id": Constants.BACKEND_ID,
	}
	var json = JSON.stringify(data)
	map_http_request.cancel_request()
	map_http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func _on_select_map_request_completed(_result: int, response_code: int, _headers: PackedStringArray, body: PackedByteArray) -> void:
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		
		var data = json["map"]
		BackendManager.map_id = data["id"]
		server.MAX_PLAYERS = data["max_players_count"]
		Constants.LOBBY_PLAYER_COUNT_START = data["min_players_count"]
		server.BOT_WAIT_REMOVE = data["bot_wait_remove"]
		server.BOT_RESET_TIME = data["bot_reset_time"]
		#print("reset time = ", server.BOT_RESET_TIME)
		
		selected_map_packed = load(data["path"])
		
		selected_map = selected_map_packed.instantiate()
		add_child(selected_map)
		print("Map Selected and Loaded: ", selected_map_packed.resource_path)
		server.map = selected_map
		server.map_packed_scene = selected_map_packed
		UniversalSyncManager.map = selected_map
		
		
		Constants.SERVER_IN_GAME_TIME = json["settings"]["game_time"]
		Constants.SERVER_PROPHAUNT_LOBBY_TIME = json["settings"]["prophaunt_lobby_time"]
		Constants.PROPHAUNT_ROUND_TIME = json["settings"]["prophaunt_round_time"]
		Constants.SERVER_COUNTDOWN_TIME = json["settings"]["countdown_time"]
		Constants.SERVER_RESULT_TIME = json["settings"]["result_time"]
		if BackendManager.server_is_test:
			Constants.LOBBY_TIME = 5
		if StageManager.current_stage > 1:
			Constants.LOBBY_TIME = 0.1
		Constants.SERVER_PLAYER_JOINED_ADD_LOBBY_TIME = json["settings"]["player_join_add_time"]
		print ("Server Settings: RoundTime=", Constants.PROPHAUNT_ROUND_TIME, " LobbyTime=", Constants.SERVER_PROPHAUNT_LOBBY_TIME)
		server.set_state_to_lobby()
	else:
		print("map select error: ", response_code)
		if response_code == 400:
			print("MAP SELECT SHOULD RESTART ASAP")
			await get_tree().create_timer(2).timeout
			StageManager.is_finished = true
			StageManager.server_instance = server
			Constants.server = null
			server.restart_server()
			server = null
			StageManager.server_instance = null
			return
		
		await get_tree().create_timer(2).timeout
		send_select_map_request()


#Client 
func on_game_server_find(_result, response_code, _headers, body):
	if response_code == 200:
		var res = JSON.parse_string(body.get_string_from_utf8())
		var json = res["best_server"]
		available_servers = []
		available_servers.append({
			"ip": json["ip"],
			"port": int(json["port"]),
			"total_stage": json["total_stage"],
		})
		available_servers.append_array(res["all_servers"])
		server_index = 0
		check_next_server()
	else:
		print(response_code, body.get_string_from_utf8())
		on_client_error_connecting()


func on_client_error_connecting():
	pass


func on_client_disconnect():
	pass


func _on_find_cancel_button_pressed() -> void:
	Selector.is_exit_game = true
	ClientRPC.disconnect_from_server()
	get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")


func create_and_add_bot():
	var random_character = Selector.get_random_character_v2()
	var bot_data = client.my_data.duplicate(true)
	bot_data["backend_id"] = -randi() % 1000
	#bot_data["selection"]["name"] = "AnimalRush#" + str(randi() % 1000)
	bot_data["selection"]["name"] = BotNameGenerator.get_random_name()
	bot_data["selection"]["character"] = random_character["path"]
	bot_data["selection"]["character_id"] = random_character["id"]
	server.new_bot_player(bot_data)
