[gd_scene load_steps=5 format=3 uid="uid://b6ast582o83jc"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_u0h1l"]
[ext_resource type="PackedScene" uid="uid://crsygjh44810o" path="res://prophaunt/maps/Source/Table/table_round.tscn" id="2_fjbyx"]

[sub_resource type="CylinderShape3D" id="CylinderShape3D_3d8ey"]
height = 0.0545436
radius = 0.403073

[sub_resource type="BoxShape3D" id="BoxShape3D_yjq6c"]
size = Vector3(0.213745, 0.317383, 0.222046)

[node name="TableRoundProp" instance=ExtResource("1_u0h1l")]

[node name="TableRound" parent="Meshes" index="0" instance=ExtResource("2_fjbyx")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.341048, 0)
shape = SubResource("CylinderShape3D_3d8ey")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D" index="1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.000549316, 0.155762, -0.0043335)
shape = SubResource("BoxShape3D_yjq6c")
