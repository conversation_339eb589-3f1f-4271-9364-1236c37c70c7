[gd_resource type="Resource" script_class="ProphauntGunItem" load_steps=4 format=3 uid="uid://d0xmcwbwsob66"]

[ext_resource type="Texture2D" uid="uid://co6u7sifpkwom" path="res://prophaunt/assets/Guns/Scene/SMG.png" id="1_38paf"]
[ext_resource type="PackedScene" uid="uid://8wh161w5vums" path="res://prophaunt/assets/Guns/Scene/smg.tscn" id="2_smg_scene"]
[ext_resource type="Script" path="res://Inventory/ProphauntGunItem.gd" id="3_smg_script"]

[resource]
script = ExtResource("3_smg_script")
salam = 10
damage = 70
ammo = 40
gun_type = "range"
range = 35
animation_speed = 5.0
scene = ExtResource("2_smg_scene")
scale = Vector3(13, 13, 4)
position = Vector3(19.5, 14.6, 0)
rotation = Vector3(-148, 194, 74)
ui = 2
id = 5
name = "SMG"
icon = ExtResource("1_38paf")
price = 400
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
