[gd_resource type="Resource" script_class="GunInventoryItem" load_steps=4 format=3 uid="uid://lzx8u3y5f6rv8"]

[ext_resource type="Texture2D" uid="uid://pm05jfwruxpr" path="res://Scenes/ui/assets/pistol.png" id="1_smg_icon"]
[ext_resource type="PackedScene" uid="uid://8wh161w5vums" path="res://prophaunt/assets/Guns/Scene/smg.tscn" id="2_smg_scene"]
[ext_resource type="Script" path="res://Inventory/GunInventoryItem.gd" id="3_smg_script"]

[resource]
script = ExtResource("3_smg_script")
damage = 70
ammo = 40
gun_type = "range"
range = 35
animation_speed = 5.0
scene = ExtResource("2_smg_scene")
scale = Vector3(150, 150, 150)
position = Vector3(0, 0, 0)
rotation = Vector3(10, 125, 90)
ui = 2
id = 111
name = "SMG"
icon = ExtResource("1_smg_icon")
price = 400
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
