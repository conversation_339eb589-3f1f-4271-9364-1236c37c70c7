[gd_scene load_steps=8 format=3 uid="uid://7hwa1a8r5cqj"]

[ext_resource type="Script" path="res://prophaunt/scripts/WeaponSelector.gd" id="1_weapon_selector"]
[ext_resource type="PackedScene" uid="uid://cixw0i4rjg3rx" path="res://Scenes/ui/ExitableControl.tscn" id="2_exitable"]
[ext_resource type="Texture2D" uid="uid://r7n4eqwqtiue" path="res://Scenes/ui/assets/gradiant_bg.png" id="3_bg"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="4_font"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="5_button"]
[ext_resource type="Texture2D" uid="uid://cxh1wnutl4gb4" path="res://Scenes/ui/assets/next.png" id="6_next"]
[ext_resource type="Texture2D" uid="uid://b010qsgl38ikk" path="res://Scenes/ui/assets/coin1.png" id="7_coin"]

[node name="WeaponSelector" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("1_weapon_selector")

[node name="HUD" parent="." instance=ExtResource("2_exitable")]
layout_mode = 1

[node name="BG" type="TextureRect" parent="HUD"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("3_bg")

[node name="WeaponTexture" type="TextureRect" parent="HUD"]
custom_minimum_size = Vector2(400, 300)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2
expand_mode = 1
stretch_mode = 4

[node name="NameLabel" type="Label" parent="HUD/WeaponTexture"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = -50.0
offset_right = 390.0
offset_bottom = -10.0
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_font")
theme_override_font_sizes/font_size = 32
text = "WEAPON NAME"
horizontal_alignment = 1
vertical_alignment = 1

[node name="NextButton" parent="HUD" instance=ExtResource("5_button")]
custom_minimum_size = Vector2(60, 60)
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = -120.0
offset_top = -30.0
offset_right = -60.0
offset_bottom = 30.0
grow_horizontal = 0
pivot_offset = Vector2(30, 30)

[node name="TextureRect" type="TextureRect" parent="HUD/NextButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_next")
expand_mode = 1
stretch_mode = 4

[node name="PreviousButton" parent="HUD" instance=ExtResource("5_button")]
custom_minimum_size = Vector2(60, 60)
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_right = 0.0
anchor_bottom = 0.5
offset_left = 60.0
offset_top = -30.0
offset_right = 120.0
offset_bottom = 30.0
grow_horizontal = 1
pivot_offset = Vector2(30, 30)

[node name="TextureRect" type="TextureRect" parent="HUD/PreviousButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_next")
expand_mode = 1
stretch_mode = 4
flip_h = true

[node name="StatsPanel" type="Panel" parent="HUD"]
visible = false
custom_minimum_size = Vector2(220, 300)
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 54.0
offset_top = -150.0
offset_right = 274.0
offset_bottom = 150.0
grow_vertical = 2
mouse_filter = 2

[node name="VBoxContainer" type="VBoxContainer" parent="HUD/StatsPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="DamageLabel" type="Label" parent="HUD/StatsPanel/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_font")
theme_override_font_sizes/font_size = 24
text = "DAMAGE: 40"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AmmoLabel" type="Label" parent="HUD/StatsPanel/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_font")
theme_override_font_sizes/font_size = 24
text = "AMMO: 12"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TypeLabel" type="Label" parent="HUD/StatsPanel/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_font")
theme_override_font_sizes/font_size = 24
text = "TYPE: MELEE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Lock" type="Control" parent="HUD"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="VBoxContainer" type="VBoxContainer" parent="HUD/Lock"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -247.0
offset_top = -200.0
offset_right = -36.0
offset_bottom = -30.0
grow_horizontal = 0
grow_vertical = 0
alignment = 2

[node name="PurchaseButton" parent="HUD/Lock/VBoxContainer" instance=ExtResource("5_button")]
custom_minimum_size = Vector2(150, 60)
layout_mode = 2

[node name="price" type="Label" parent="HUD/Lock/VBoxContainer/PurchaseButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_font")
theme_override_font_sizes/font_size = 24
text = "1000"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TextureRect" type="TextureRect" parent="HUD/Lock/VBoxContainer/PurchaseButton"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -40.0
offset_top = -15.0
offset_right = -10.0
offset_bottom = 15.0
grow_horizontal = 0
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("7_coin")
expand_mode = 1
stretch_mode = 4

[node name="SelectButton" parent="HUD" instance=ExtResource("5_button")]
custom_minimum_size = Vector2(150, 60)
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
offset_left = -247.0
offset_top = -130.0
offset_right = -97.0
offset_bottom = -70.0
grow_horizontal = 0
grow_vertical = 0
pivot_offset = Vector2(75, 30)

[node name="Label" type="Label" parent="HUD/SelectButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_font")
theme_override_font_sizes/font_size = 24
text = "SELECT"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Loading" type="Control" parent="HUD"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="HUD/Loading"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="LoadingLabel" type="Label" parent="HUD/Loading"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -25.0
offset_right = 100.0
offset_bottom = 25.0
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("4_font")
theme_override_font_sizes/font_size = 32
text = "LOADING"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ListHTTPRequest" type="HTTPRequest" parent="."]

[node name="PurchaseHTTPRequest" type="HTTPRequest" parent="."]

[connection signal="pressed" from="HUD/NextButton" to="." method="_on_next_button_pressed"]
[connection signal="pressed" from="HUD/PreviousButton" to="." method="_on_previous_button_pressed"]
[connection signal="pressed" from="HUD/Lock/VBoxContainer/PurchaseButton" to="." method="_on_purchase_button_pressed"]
[connection signal="pressed" from="HUD/SelectButton" to="." method="_on_select_button_pressed"]
