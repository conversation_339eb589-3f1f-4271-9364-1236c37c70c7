class_name RifleShootState
extends Node

@onready var character: Character = $"../.."

var counter = 0
var total_time = 0
var has_shot = false
var shot_time = 0

func _ready() -> void:
	pass

func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return
	
	counter += delta
	
	# Handle shooting at the right time in animation
	if counter >= shot_time and has_shot == false:
		has_shot = true
		on_shot_fired()
	
	var input_dir
	if counter >= total_time:
		# Shooting animation finished, return to appropriate rifle state
		input_dir = character.player_client_input(delta)
		if input_dir != Vector2(0, 0):
			character.state = character.State.RIFLE_RUN
		else:
			character.state = character.State.RIFLE_IDLE
		return
	
	var just_jumped = character.player_client_jump()
	input_dir = character.player_client_input(delta)
	
	# Allow jumping during shoot (if you want this behavior)
	if just_jumped:
		character.state = character.State.RIFLE_JUMP
		return
	
	# Check for switching back to normal states (e.g., when weapon is holstered)
	if Input.is_action_just_pressed("holster_weapon"):
		character.state = character.State.STATE_IDLE
		return
	
	character.handle_rifle_mode(delta, true)

func start_state():
	if character.state == character.State.RIFLE_SHOOT:
		return
	
	character.state = character.State.RIFLE_SHOOT
	counter = 0
	has_shot = false
	
	# Get shoot animation length
	var animation_name = character.RIFLE_SHOOT_ANIMATION
	if character.animation_player.has_animation(animation_name):
		total_time = character.animation_player.get_animation(animation_name).length
		# Set shot time to be partway through animation (adjust as needed)
		shot_time = total_time * 0.3  # Fire at 30% through animation
	else:
		total_time = 0.5  # Default shoot time
		shot_time = 0.15  # Default shot timing
	
	# Animation will be handled in handle_animation function

func end_state():
	counter = 0
	has_shot = false

func on_shot_fired():
	# This can be connected to shooting logic (e.g., spawn bullet, reduce ammo)
	print("Rifle shot fired!")
	# You can emit a signal here or call shooting logic
	# Example: character.emit_signal("rifle_shot_fired")
	
	# Add muzzle flash, sound effects, etc. here
	if Constants.is_client() and character.controls == Constants.Controls.Player:
		# Play shooting sound
		# SoundManager.play_rifle_shot_sound()
		pass
