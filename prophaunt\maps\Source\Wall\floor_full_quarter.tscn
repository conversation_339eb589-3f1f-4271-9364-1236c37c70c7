[gd_scene load_steps=5 format=4 uid="uid://c1dbf426l5k4r"]

[ext_resource type="Material" uid="uid://lhnii45g7kv1" path="res://prophaunt/Mat/DarkLightBrown.tres" id="1_yg60b"]

[sub_resource type="ArrayMesh" id="ArrayMesh_rpuo4"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 96,
"index_data": PackedByteArray("CAABAAAACAALAAEAEQABAAsAEQANAAEADAABAA0ADAAAAAEACQAGAAUACQAKAAYAEAACAAkAEAAMAAIADwAEAA4ADwAHAAQADgAJAAUADgAQAAkAAgAKAAkAAgADAAoADwALAAcADwARAAsABAALAAgABAAHAAsABgARAA8ABgAKABEABAAQAA4ABAAIABAABgAOAAUABgAPAA4ACAAMABAACAAAAAwAAgANAAMAAgAMAA0ACgANABEACgADAA0A"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 18,
"vertex_data": PackedByteArray("///+/wAAAAD//wAAAAAAAP///////wAA//8AAP//AAAAAP7/AAAAAAAA/////wAAAAAAAP//AAAAAAAAAAAAAP9//v8AAAAA/3//////AAD/fwAA//8AAP9/AAAAAAAA///+//9/AAD//wAA/38AAAAA/v//fwAAAAAAAP9/AAD/f/7//38AAP9/AAD/fwAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_p5qin"]
resource_name = "FloorFullQuarterUV_FloorFullQuarter"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("X3aowuhwA4xfdqjC6HCowkM6TfnocKjC2/aEw3+DcvpZ/JWMXfGEw13xlYzb9pWM2/aEw266hMNd8XL61nuowp8DqMLWe6jCX3aowuhwqMJfdqjCWfyEw3+DlYzb9nL6X3ZN+dv2cvpDOgOMXfGEw9Z7Tfnb9pWMQzqown+DhMNfdk35XfGVjOhwTfluupWM6HBN+Vn8cvqfA035f4OEw+hwqMJuunL6X3YDjFn8hMNDOqjCXfGEw+hwA4zb9oTDnwMDjG66cvrWewOMXfGEw0M6qMJuupWMX3YDjNv2hMNDOgOMQzqowm66hMNd8XL6nwOowkM6TfluuoTD"),
"format": 34896613399,
"index_count": 96,
"index_data": PackedByteArray("GAADAAAAGAAkAAMAPAAEACYAPAAsAAQAKgAFAC4AKgACAAUAHAASAA8AHAAgABIAOgAHAB8AOgApAAcANwAOADMANwAXAA4AMAAeABAAMAA4AB4ABgAhAB0ABgAJACEANQAnABYANQA+ACcADAAlABkADAAVACUAEwA9ADQAEwAiAD0ADQA7ADEADQAbADsAFAAyABEAFAA2ADIAGgAoADkAGgABACgACAAvAAsACAArAC8AIwAtAD4AIwAKAC0A"),
"material": ExtResource("1_yg60b"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 63,
"vertex_data": PackedByteArray("///+/wAA//////7/AAD/v////v8AAFTV//8AAAAA/////wAAAAD/v///AAAAAFTV/////////7////////9U1f///////1TV//8AAP///7///wAA////v///AAD//1TVAAD+/wAA//8AAP7/AAD/vwAA/v8AAFTVAAD//////78AAP//////vwAA/////1TVAAAAAP///78AAAAA//9U1QAAAAD//1TVAAAAAAAA//8AAAAAAABU1QAAAAAAAFTV/3/+/wAA////f/7/AAD///9//v8AAP+//3/+/wAA/7//f///////v/9///////+//3///////7//f/////9U1f9/AAD///+//38AAP///7//fwAA//9U1f9/AAD///+//38AAAAA////fwAAAAD///9/AAAAAP+//38AAAAAVNX///7//3//v////v//f1TV///+//9/VNX///7//39U1f//AAD/f/+///8AAP9//7///wAA/39U1f//AAD/f1TVAAD+//9//78AAP7//3//vwAA/v//f1TVAAD+//9/VNUAAAAA/39U1QAAAAD/f1TVAAAAAP9/VNUAAAAA/39U1f9//v//f/+//3/+//9//7//f/7//39U1f9//v//f/+//38AAP9//7//fwAA/39U1f9/AAD/f/+//7//v////3+qKqoq/7//vwAA/3+qKqoq/////1TVqiqqKqoq/////wAA/3+qKqoq/z//v////39UVaqq/////////39U1VTV/////6oqVNVU1VTV/z//v6oqVNVUVaqq/7//v/8//7////9/////f///////////////f1TVqir//////////6oqVNUAAP9//7//v/8//78AAP9/qipU1f///39U1aoqqiqqKqoqqioAAP9/AAD/f6oqqiqqKqoq////f////39U1VTVVFWqqqoqVNWqKlTVVNVU1VRVqqr///9/////f1TVqir///9/AAD/f6oqVNUAAP9/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_rpuo4")

[sub_resource type="BoxShape3D" id="BoxShape3D_rxrnm"]
size = Vector3(2, 0.1, 2)

[node name="FloorFullQuarter" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_p5qin")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2.98023e-08, -0.05, 0)
shape = SubResource("BoxShape3D_rxrnm")
