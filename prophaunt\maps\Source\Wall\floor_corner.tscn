[gd_scene load_steps=5 format=4 uid="uid://c7et302mptk4f"]

[ext_resource type="Material" uid="uid://lhnii45g7kv1" path="res://prophaunt/Mat/DarkLightBrown.tres" id="1_dqwak"]

[sub_resource type="ArrayMesh" id="ArrayMesh_ps26f"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 60,
"index_data": PackedByteArray("CQAEAAYACQAIAAQABQAKAAIABQAGAAoACAALAAEACAAJAAsAAgALAAMAAgAKAAsAAgAHAAUAAgADAAcACAAAAAQACAABAAAABwAGAAUABwAJAAYABgAAAAoABgAEAAAACQADAAsACQAHAAMACgABAAsACgAAAAEA"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 12,
"vertex_data": PackedByteArray("///+/wAAAAD//wAAAAAAAP///////wAA//8AAP//AAAyM/7/AAAAAAAA/////wAAAAD+/zIzAAAAAAAA//8AADIzAAAAAAAAAAAAADIzAAD///7/MjMAAP//AAAyMwAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_bb634"]
resource_name = "FloorCornerUV_FloorCorner"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("SoR0xC/7d6BZhEDcTYRnyin7EqZVhE3WPvtDuHCDOvt64zjEQvs2vtmEf+t240W+euM4xP+bs6BRhFrQQoSOuCSDPoR+4yvKQoSOuFTiAoT5pUXQTYRnykaEgb7W+zPrgeMe0H7jK8r8m6mkVYRN1ouEg4yZ+wOM/KU41lGEWtCg4v76PvtDuEqEdMQlnDHcnYROjIT7R45GhIG+IZw+1g=="),
"format": 34896613399,
"index_count": 60,
"index_data": PackedByteArray("HgAOABQAHgAbAA4AEAAgAAcAEAATACAAGgAlAAQAGgAcACUACAAmAAsACAAiACYABgAWAA8ABgAJABYAGQAAAAwAGQADAAAAGAAVABEAGAAfABUAEgABACEAEgANAAEAHQAKACQAHQAXAAoAIwAFACcAIwACAAUA"),
"material": ExtResource("1_dqwak"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("///+/wAA//////7/AAD/v////v8AAOz///8AAAAA/////wAAAAD/v///AAAAAOz/////////GYD///////9F1f///////+z///8AAP//GYD//wAA///s////AAD//+z/MjP+/wAA//8yM/7/AAD/vzIz/v8AAP/fAAD/////GYAAAP////9F1QAA//////+/AAD+/zIz/78AAP7/MjNF1QAA/v8yM//fAAD+/zIz/78AAAAA//8ZgAAAAAD//+z/AAAAAP///78yMwAAAAD//zIzAAAAAP+/MjMAAAAA/98AAAAAMjP/vwAAAAAyM+z/AAAAADIz/98AAAAAMjP/v////v8yM0XV///+/zIz/7////7/MjPs/////v8yM+z///8AADIz7P///wAAMjP/v///AAAyM+z///8AADIz7P/Wf9b/rv8ngBQA9T/Wf9b/7wDegRQA9T//f/9/XdW8KhMA9T//f/9/9T/q/xQA9T/Wf9b/rv8ngNZ/xf//f/9/XdW8Ktd/rv+u/yeAXdW8KtZ/xf/Xf67//3//f/U/6v/Xf67/1n/W/+8A3oHWf8X/7wDegfU/6v/Wf8X/13+u/13VvCqu/yeAFAD1PxQA9T/1P+r/7wDegRQA9T8UAPU/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_ps26f")

[sub_resource type="BoxShape3D" id="BoxShape3D_l6ook"]
size = Vector3(2, 0.1, 2)

[node name="FloorCorner" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_bb634")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2.98023e-08, -0.05, 0)
shape = SubResource("BoxShape3D_l6ook")
