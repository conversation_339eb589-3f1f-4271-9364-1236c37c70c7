[gd_scene load_steps=4 format=3 uid="uid://dvgp1wmt4d53y"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_ss0c1"]
[ext_resource type="PackedScene" uid="uid://bmyqpnlkqx33r" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/BasketCheese.tscn" id="2_7i123"]

[sub_resource type="BoxShape3D" id="BoxShape3D_1nqqm"]
size = Vector3(2.02954, 1.00952, 2.0448)

[node name="CheeseBasketProp" instance=ExtResource("1_ss0c1")]

[node name="BasketCheese" parent="Meshes" index="0" instance=ExtResource("2_7i123")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00549316, 0.504517, 0.00482178)
shape = SubResource("BoxShape3D_1nqqm")
