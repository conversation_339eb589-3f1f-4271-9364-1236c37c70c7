extends Node
class_name ProphauntHealthSystem

# Prophaunt health and damage system
# Handles HP, damage calculation, and health display

var character: Character
var prophaunt_player: ProphauntPlayer

# Health data
var max_hp: int = Constants.PROPHAUNT_PROP_DEFAULT_HP
var current_hp: int = Constants.PROPHAUNT_PROP_DEFAULT_HP
var is_invulnerable: bool = false
var invulnerability_duration: float = 0.0

# Damage tracking
var damage_taken_this_round: int = 0
var last_damage_source: String = ""
var last_damage_time: float = 0.0

# Health display
var health_bar: ProgressBar
var health_label: Label
var damage_indicator: Label

# Damage effects
var damage_flash_material: Material
var original_material: Material

signal health_changed(new_hp: int, max_hp: int)
signal damage_taken(damage: int, source: String)
signal player_died()
signal health_regenerated(amount: int)

func _ready():
	setup_health_display()

func initialize(player_character: Character, prophaunt_comp: ProphauntPlayer):
	"""Initialize the health system"""
	character = player_character
	prophaunt_player = prophaunt_comp
	
	# Set initial health based on team
	if prophaunt_player.team == Constants.ProphauntTeam.PROPS:
		max_hp = Constants.PROPHAUNT_PROP_DEFAULT_HP
		current_hp = max_hp
	else:
		# Haunters don't have HP in traditional sense
		max_hp = 100
		current_hp = max_hp
	
	# Setup visual components
	setup_damage_effects()
	update_health_display()

func setup_health_display():
	"""Set up the health display UI"""
	# This would typically be done in the UI layer
	# For now, we'll create basic UI elements
	
	# Health bar (would be positioned above character)
	health_bar = ProgressBar.new()
	health_bar.name = "HealthBar"
	health_bar.size = Vector2(60, 8)
	health_bar.show_percentage = false
	
	# Health label
	health_label = Label.new()
	health_label.name = "HealthLabel"
	health_label.size = Vector2(60, 20)
	health_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	
	# Damage indicator
	damage_indicator = Label.new()
	damage_indicator.name = "DamageIndicator"
	damage_indicator.modulate = Color.RED
	damage_indicator.visible = false

func setup_damage_effects():
	"""Set up visual damage effects"""
	if not character:
		return
	
	# Store original material for damage flash effect
	var mesh_instance = character.get_node_or_null("MeshInstance3D")
	if mesh_instance and mesh_instance.get_surface_override_material_count() > 0:
		original_material = mesh_instance.get_surface_override_material(0)
	
	# Create damage flash material
	damage_flash_material = StandardMaterial3D.new()
	damage_flash_material.albedo_color = Color.RED
	damage_flash_material.emission_enabled = true
	damage_flash_material.emission = Color.RED * 0.5

func take_damage(damage: int, source: String = "unknown") -> bool:
	"""Apply damage to the player"""
	if is_invulnerable:
		return false
	
	if prophaunt_player.team != Constants.ProphauntTeam.PROPS:
		# Only props take damage in Prophaunt
		return false
	
	if prophaunt_player.player_state != Constants.ProphauntPlayerState.ALIVE:
		return false
	
	# Apply damage
	var actual_damage = calculate_damage(damage, source)
	current_hp -= actual_damage
	current_hp = max(0, current_hp)
	
	# Track damage
	damage_taken_this_round += actual_damage
	last_damage_source = source
	last_damage_time = Time.get_ticks_msec() / 1000.0
	
	# Emit signals
	health_changed.emit(current_hp, max_hp)
	damage_taken.emit(actual_damage, source)
	
	# Visual effects
	show_damage_effect(actual_damage)
	flash_damage_effect()
	
	# Check for death
	if current_hp <= 0:
		die()
		return true
	
	# Brief invulnerability after taking damage
	set_invulnerable(0.5)  # 0.5 seconds
	
	update_health_display()
	return true

func calculate_damage(base_damage: int, source: String) -> int:
	"""Calculate actual damage based on various factors"""
	var actual_damage = base_damage
	
	# Apply damage modifiers based on source
	match source:
		"gun":
			actual_damage = Constants.PROPHAUNT_GUN_DAMAGE
		"grenade":
			actual_damage = Constants.PROPHAUNT_GRENADE_DAMAGE
		"self_damage":
			actual_damage = Constants.PROPHAUNT_SELF_DAMAGE
	
	# Apply any damage reduction effects
	if prophaunt_player.disguise_system and prophaunt_player.disguise_system.is_disguised():
		# Some disguises might provide damage reduction
		var disguise_type = prophaunt_player.disguise_system.get_current_disguise()
		match disguise_type:
			"barrel", "box":
				actual_damage = int(actual_damage * 0.9)  # 10% damage reduction
	
	return max(1, actual_damage)  # Minimum 1 damage

func heal(amount: int) -> bool:
	"""Heal the player"""
	if current_hp >= max_hp:
		return false
	
	current_hp += amount
	current_hp = min(max_hp, current_hp)
	
	health_changed.emit(current_hp, max_hp)
	health_regenerated.emit(amount)
	
	update_health_display()
	return true

func set_invulnerable(duration: float):
	"""Set temporary invulnerability"""
	is_invulnerable = true
	invulnerability_duration = duration
	
	# Visual indication of invulnerability
	if character:
		var tween = create_tween()
		tween.tween_method(set_invulnerability_alpha, 1.0, 0.5, 0.1)
		tween.tween_method(set_invulnerability_alpha, 0.5, 1.0, 0.1)
		tween.set_loops(int(duration / 0.2))

func set_invulnerability_alpha(alpha: float):
	"""Set character alpha for invulnerability effect"""
	if character:
		character.modulate.a = alpha

func die():
	"""Handle player death"""
	prophaunt_player.player_state = Constants.ProphauntPlayerState.DEAD
	player_died.emit()
	
	# Visual death effects
	show_death_effect()
	
	# Disable character
	if character:
		character.disable()
	
	print("Player died with ", damage_taken_this_round, " total damage taken")

func reset_health():
	"""Reset health to full (for new rounds)"""
	current_hp = max_hp
	damage_taken_this_round = 0
	last_damage_source = ""
	is_invulnerable = false
	invulnerability_duration = 0.0
	
	health_changed.emit(current_hp, max_hp)
	update_health_display()

func update_health_display():
	"""Update the health display UI"""
	if health_bar:
		health_bar.max_value = max_hp
		health_bar.value = current_hp
	
	if health_label:
		health_label.text = str(current_hp) + "/" + str(max_hp)
	
	# Update health bar color based on health percentage
	if health_bar:
		var health_percent = float(current_hp) / float(max_hp)
		if health_percent > 0.6:
			health_bar.modulate = Color.GREEN
		elif health_percent > 0.3:
			health_bar.modulate = Color.YELLOW
		else:
			health_bar.modulate = Color.RED

func show_damage_effect(damage: int):
	"""Show visual damage effect"""
	if damage_indicator:
		damage_indicator.text = "-" + str(damage)
		damage_indicator.visible = true
		
		# Animate damage indicator
		var tween = create_tween()
		tween.parallel().tween_property(damage_indicator, "position:y", damage_indicator.position.y - 30, 1.0)
		tween.parallel().tween_property(damage_indicator, "modulate:a", 0.0, 1.0)
		tween.tween_callback(func(): damage_indicator.visible = false)

func flash_damage_effect():
	"""Flash the character red when taking damage"""
	if not character or not damage_flash_material:
		return
	
	var mesh_instance = character.get_node_or_null("MeshInstance3D")
	if not mesh_instance:
		return
	
	# Apply red flash
	mesh_instance.set_surface_override_material(0, damage_flash_material)
	
	# Restore original material after a short time
	get_tree().create_timer(0.2).timeout.connect(func():
		if mesh_instance and original_material:
			mesh_instance.set_surface_override_material(0, original_material)
	)

func show_death_effect():
	"""Show visual death effect"""
	if not character:
		return
	
	# Create death particles or effects
	print("Death effect for player at: ", character.global_position)
	
	# Play death sound
	SoundManager.play_3d_sound.rpc_id(1, character.global_position, 15, SoundManager.SOUND_TYPE.ExplosionMedium)

func _process(delta):
	"""Update health system"""
	# Update invulnerability
	if is_invulnerable:
		invulnerability_duration -= delta
		if invulnerability_duration <= 0:
			is_invulnerable = false
			if character:
				character.modulate.a = 1.0

# Getters
func get_health_percentage() -> float:
	"""Get health as a percentage"""
	return float(current_hp) / float(max_hp)

func get_current_hp() -> int:
	"""Get current HP"""
	return current_hp

func get_max_hp() -> int:
	"""Get maximum HP"""
	return max_hp

func is_alive() -> bool:
	"""Check if player is alive"""
	return current_hp > 0 and prophaunt_player.player_state == Constants.ProphauntPlayerState.ALIVE

func is_critically_injured() -> bool:
	"""Check if player is critically injured"""
	return get_health_percentage() < 0.25

func get_damage_taken_this_round() -> int:
	"""Get total damage taken this round"""
	return damage_taken_this_round

func get_last_damage_source() -> String:
	"""Get the source of the last damage"""
	return last_damage_source
