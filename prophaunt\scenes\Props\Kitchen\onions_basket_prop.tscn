[gd_scene load_steps=4 format=3 uid="uid://cgbyscokd2lua"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_fi5cp"]
[ext_resource type="PackedScene" uid="uid://boyi6t34830iu" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/BasketOnions.tscn" id="2_gwkf2"]

[sub_resource type="BoxShape3D" id="BoxShape3D_onpga"]
size = Vector3(2.0271, 1.30322, 2.04614)

[node name="OnionsBasketProp" instance=ExtResource("1_fi5cp")]

[node name="BasketOnions" parent="Meshes" index="0" instance=ExtResource("2_gwkf2")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00622559, 0.632568, 0.00305176)
shape = SubResource("BoxShape3D_onpga")
