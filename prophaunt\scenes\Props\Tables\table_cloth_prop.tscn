[gd_scene load_steps=4 format=3 uid="uid://b1nerxjjtvwnb"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_okthe"]
[ext_resource type="PackedScene" uid="uid://bw5wubiy0tvm6" path="res://prophaunt/maps/Source/Table/table_cloth.tscn" id="2_wk1ay"]

[sub_resource type="BoxShape3D" id="BoxShape3D_5kgdh"]
size = Vector3(0.845947, 0.332275, 0.446423)

[node name="TableClothProp" instance=ExtResource("1_okthe")]

[node name="TableCloth" parent="Meshes" index="0" instance=ExtResource("2_wk1ay")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00134277, 0.165405, -0.000286847)
shape = SubResource("BoxShape3D_5kgdh")
