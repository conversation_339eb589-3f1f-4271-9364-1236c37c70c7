[gd_scene load_steps=4 format=3 uid="uid://bk2s1hduf81qw"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_mcnws"]
[ext_resource type="PackedScene" uid="uid://cjrtspayjscem" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/StoveMulti.tscn" id="2_wnv0m"]

[sub_resource type="BoxShape3D" id="BoxShape3D_c6msy"]
size = Vector3(2.0199, 1.19989, 2.24487)

[node name="StoveMultiCounterProp" node_paths=PackedStringArray("export_mesh") instance=ExtResource("1_mcnws")]
export_mesh = NodePath("Meshes/StoveMulti")

[node name="StoveMulti" parent="Meshes" index="0" instance=ExtResource("2_wnv0m")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00372314, 0.592621, 0.0821533)
shape = SubResource("BoxShape3D_c6msy")
