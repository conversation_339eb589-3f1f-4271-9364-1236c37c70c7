[gd_scene load_steps=4 format=3 uid="uid://d7ihbyu7nmgd"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_n4fvu"]
[ext_resource type="PackedScene" uid="uid://2ujgnn1qff6c" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/Fridge.tscn" id="2_pphjq"]

[sub_resource type="BoxShape3D" id="BoxShape3D_ypurp"]
size = Vector3(2, 2.5, 2.1)

[node name="FridgeProp" node_paths=PackedStringArray("export_mesh") instance=ExtResource("1_n4fvu")]
export_mesh = NodePath("Meshes/Fridge")

[node name="Fridge" parent="Meshes" index="0" instance=ExtResource("2_pphjq")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.25, 0.0500001)
shape = SubResource("BoxShape3D_ypurp")
