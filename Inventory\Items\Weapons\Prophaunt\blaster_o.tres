[gd_resource type="Resource" script_class="GunInventoryItem" load_steps=4 format=3 uid="uid://hvt4q9u1b2nr4"]

[ext_resource type="Texture2D" uid="uid://pm05jfwruxpr" path="res://Scenes/ui/assets/pistol.png" id="1_blaster_o_icon"]
[ext_resource type="PackedScene" uid="uid://cu83k1au1clqk" path="res://prophaunt/assets/Guns/Scene/blaster_o.tscn" id="2_blaster_o_scene"]
[ext_resource type="Script" path="res://Inventory/GunInventoryItem.gd" id="3_blaster_o_script"]

[resource]
script = ExtResource("3_blaster_o_script")
damage = 105
ammo = 16
gun_type = "range"
range = 52
animation_speed = 2.6
scene = ExtResource("2_blaster_o_scene")
scale = Vector3(150, 150, 150)
position = Vector3(0, 0, 0)
rotation = Vector3(10, 125, 90)
ui = 2
id = 107
name = "BLASTER_O"
icon = ExtResource("1_blaster_o_icon")
price = 380
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
