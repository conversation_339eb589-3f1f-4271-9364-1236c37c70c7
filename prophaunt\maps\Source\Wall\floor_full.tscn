[gd_scene load_steps=5 format=4 uid="uid://cf7j12u33go1h"]

[ext_resource type="Material" uid="uid://lhnii45g7kv1" path="res://prophaunt/Mat/DarkLightBrown.tres" id="1_if1hh"]

[sub_resource type="ArrayMesh" id="ArrayMesh_mg3ht"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 36,
"index_data": PackedByteArray("BQAAAAIABQAEAAAAAgAGAAUAAgADAAYABAABAAAABAAHAAEAAgABAAMAAgAAAAEABgAEAAUABgAHAAQABwADAAEABwAGAAMA"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("///+/wAAAAD//wAAAAAAAP///////wAA//8AAP//AAAAAP7/AAAAAAAA/////wAAAAAAAP//AAAAAAAAAAAAAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_tb70h"]
resource_name = "FloorFullUV_FloorFull"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("Zbtbh/D3lImTzOT5q7Vbhx2Ij/rZxuT5H8Hk+fD3HfyTzFuHZbvk+ab6j/rZxluHZbvk+WeFlIkfweT5H8Fbh2eFHfwfwVuHZbtbh6b6BojZxluHq7Xk+R2IBojZxuT5"),
"format": 34896613399,
"index_count": 36,
"index_data": PackedByteArray("EAABAAcAEAANAAEABgASAA8ABgAJABIADAADAAAADAAVAAMACAAFAAsACAACAAUAFAAOABEAFAAXAA4AFgAKAAQAFgATAAoA"),
"material": ExtResource("1_if1hh"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 24,
"vertex_data": PackedByteArray("///+/wAA//////7/AAD/v////v8AAFTV//8AAAAA/////wAAAABU1f//AAAAAFTV/////////7//////////v////////1TV//8AAP///7///wAA//9U1f//AAD//1TVAAD+/wAA//8AAP7/AAD/vwAA/v8AAFTVAAD//////78AAP//////vwAA/////1TVAAAAAP///78AAAAA//9U1QAAAAD//1TVAAAAAAAA//8AAAAAAABU1QAAAAAAAFTV/7//v////3+qKqoq/7//v6oqVNWqKqoq/////////3+qKqoq/////6oqVNWqKqoq/7//v////39UVaqq/////////39UVaqq/////6oqVNVUVaqq/7//v6oqVNVUVaqq")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_mg3ht")

[sub_resource type="BoxShape3D" id="BoxShape3D_1oxdn"]
size = Vector3(2, 0.1, 2)

[node name="FloorFull" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_tb70h")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2.98023e-08, -0.05, 0)
shape = SubResource("BoxShape3D_1oxdn")
