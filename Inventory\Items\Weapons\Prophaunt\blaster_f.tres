[gd_resource type="Resource" script_class="GunInventoryItem" load_steps=4 format=3 uid="uid://ftr2o7s9z0lp2"]

[ext_resource type="Texture2D" uid="uid://pm05jfwruxpr" path="res://Scenes/ui/assets/pistol.png" id="1_blaster_f_icon"]
[ext_resource type="PackedScene" uid="uid://c63q1k1g6qht2" path="res://prophaunt/assets/Guns/Scene/blaster_f.tscn" id="2_blaster_f_scene"]
[ext_resource type="Script" path="res://Inventory/GunInventoryItem.gd" id="3_blaster_f_script"]

[resource]
script = ExtResource("3_blaster_f_script")
damage = 85
ammo = 18
gun_type = "range"
range = 40
animation_speed = 3.2
scene = ExtResource("2_blaster_f_scene")
scale = Vector3(150, 150, 150)
position = Vector3(0, 0, 0)
rotation = Vector3(10, 125, 90)
ui = 2
id = 105
name = "BLASTER_F"
icon = ExtResource("1_blaster_f_icon")
price = 280
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
