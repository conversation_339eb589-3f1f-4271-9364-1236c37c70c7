[gd_scene load_steps=5 format=4 uid="uid://u7uqaanbdybo"]

[ext_resource type="Material" uid="uid://lhnii45g7kv1" path="res://prophaunt/Mat/DarkLightBrown.tres" id="1_h0jrj"]

[sub_resource type="ArrayMesh" id="ArrayMesh_sl3vx"]
_surfaces = [{
"aabb": AABB(-1, -0.00292158, -1.2, 2, 2, 0.4),
"format": 34896613377,
"index_count": 720,
"index_data": PackedByteArray("GQANAAwAGQAYAA0AJAAOAA8AJAAlAA4ARgAAAEEARgAaAAAALgANAC8ALgAMAA0AWAAHACEAWABQAAcAVgAMAFMAVgAZAAwATwAKAEIATwA+AAoASQAPAEMASQAkAA8ATAABAEMATAAwAAEAPwAKAD4APwALAAoAWwAEAFMAWwAtAAQAXgAJAFIAXgA7AAkAVwATAFQAVwAfABMARwAQAEQARwAcABAAHwASABMAHwAeABIASAAWAEUASAAiABYAWQAVACcAWQBVABUAIgAXABYAIgAjABcAVAAZAFYAVAATABkARAAaAEYARAAQABoAEwAYABkAEwASABgACQAeAB8ACQAIAB4AQAAcAEcAQAACABwAUgAfAFcAUgAJAB8ACgAjACIACgALACMAQgAiAEgAQgAKACIAVQAhABUAVQBYACEAUQAnAAUAUQBZACcARQAkAEkARQAWACQAFgAlACQAFgAXACUAXAApAFoAXAAxACkASwAoAEoASwAsACgAMgArADMAMgAqACsAQQAsAEsAQQAAACwAWgAtAFsAWgApAC0AKgAvACsAKgAuAC8ADgAzAA8ADgAyADMAUQAxAFwAUQAFADEASgAwAEwASgAoADAAXwA3AF0AXwA/ADcAOwA2ADoAOwA3ADYATgA2AE0ATgA6ADYAQAA6AE4AQAAIADoACQA6AAgACQA7ADoAXQA7AF4AXQA3ADsAUAA/AF8AUAALAD8ANwA+ADYANwA/AD4ATQA+AE8ATQA2AD4ANABPADwANABNAE8AAgBOADgAAgBAAE4AOABNADQAOABOAE0AKwBMADMAKwBKAEwADQBLAC8ADQBBAEsALwBKACsALwBLAEoAFABJACYAFABFAEkAAwBIACAAAwBCAEgACABHAB4ACABAAEcAEgBGABgAEgBEAEYAIABFABQAIABIAEUAHgBEABIAHgBHAEQAMwBDAA8AMwBMAEMAJgBDAAEAJgBJAEMAPABCAAMAPABPAEIAGABBAA0AGABGAEEABwBfAD0ABwBQAF8ANQBeADkANQBdAF4APQBdADUAPQBfAF0ADgBcADIADgBRAFwAKgBbAC4AKgBaAFsAMgBaACoAMgBcAFoADgBZAFEADgAlAFkAFwBYAFUAFwAjAFgABgBXAB0ABgBSAFcAEQBWABsAEQBUAFYAJQBVAFkAJQAXAFUAHQBUABEAHQBXAFQAOQBSAAYAOQBeAFIALgBTAAwALgBbAFMAGwBTAAQAGwBWAFMAIwBQAFgAIwALAFAAdQBxAHAAdQB6AHEAdgBwAHcAdgByAHAAaQBoAG4AaQBqAGgAbgBvAG0AbgBoAG8AdwB/AHYAdwB+AH8AYABkAGUAYABiAGQAZwBhAGYAZwBjAGEAYABmAGEAYABlAGYAYwACAAMAYwBiAAIAfgB9AH8AfgB8AH0AAQBgAGEAAQAAAGAAYwABAGEAYwADAAEAZABjAGcAZABiAGMAawBpAGwAawBqAGkABABoAAYABABvAGgAZABtAGUAZABuAG0AawBtAG8AawBsAG0AcwB2AHkAcwByAHYAYAACAGIAYAAAAAIAaAAHAAYAaABqAAcAZgBpAGcAZgBsAGkAewBzAHkAewBxAHMAfQCBAH8AfQCAAIEAcgB4AHQAcgBzAHgAcgB1AHAAcgB0AHUAcQB4AHMAcQB6AHgAaQBkAGcAaQBuAGQAZgBtAGwAZgBlAG0AgQCDAIIAgQCAAIMABABrAG8ABAAFAGsAdQCDAHoAdQB8AIMAegCAAHgAegCDAIAAewB+AHcAewCCAH4AgQB7AHkAgQCCAHsAfQB4AIAAfQB0AHgAgQB2AH8AgQB5AHYAfQB1AHQAfQB8AHUAcAB7AHcAcABxAHsABwBrAAUABwBqAGsAfgCDAHwAfgCCAIMA"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 132,
"vertex_data": PackedByteArray("/7+ezP+/AAD/P57M/78AAP+/Qzb/vwAA/z9DNv+/AAD/v57M/z8AAP8/nsz/PwAA/79DNv8/AAD/P0M2/z8AAP//AABUswAA//8AAKtMAAAAAAAAVLMAAAAAAACrTAAA/////6tMAAD/////VLMAAAAA//+rTAAAAAD//1SzAAD/v/9/VLMAAP+//3+rTAAA////f1SzAAD///9/q0wAAP8//39UswAA/z//f6tMAAAAAP9/VLMAAAAA/3+rTAAA//+OglSzAAD//46Cq0wAAP+/joL/vwAA/7+Ogv8/AAD/v3B9/78AAP+/cH3/PwAA//9wfVSzAAD//3B9q0wAAP8/cH3/vwAA/z9wff8/AAAAAHB9VLMAAAAAcH2rTAAAAACOglSzAAAAAI6Cq0wAAP8/joL/vwAA/z+Ogv8/AAD/f57MVLMAAP9/nsyrTAAA/3///6tMAAD/f///VLMAAI6Cnsz/vwAAjoKezP8/AACOgv//q0wAAI6C//9UswAAcH2ezP+/AABwfZ7M/z8AAHB9//+rTAAAcH3//1SzAAD/f0M2VLMAAP9/QzarTAAA/38AAFSzAAD/fwAAq0wAAI6CQzb/vwAAjoJDNv8/AACOggAAVLMAAI6CAACrTAAAcH1DNv+/AABwfUM2/z8AAHB9AABUswAAcH0AAKtMAABv/Y8C/78AAG/9b/3/vwAAjwKPAv+/AACPAm/9/78AAG/9/39UswAAjwL/f1SzAABv/Y6C/78AAG/9cH3/vwAAjwJwff+/AACPAo6C/78AAP9/b/1UswAAjoJv/f+/AABwfW/9/78AAP9/jwJUswAAjoKPAv+/AABwfY8C/78AAI8CjwL/PwAAjwJv/f8/AABv/Y8C/z8AAG/9b/3/PwAAb/3/f6tMAACPAv9/q0wAAG/9joL/PwAAb/1wff8/AACPAnB9/z8AAI8CjoL/PwAA/39v/atMAACOgm/9/z8AAHB9b/3/PwAA/3+PAqtMAACOgo8C/z8AAHB9jwL/PwAA/7+ezP7/AAD/P57M/v8AAP+/Qzb//wAA/z9DNv//AAAysw1A/v8AADKz/7/+/wAAzEz/v/7/AADMTA1A/v8AAP+/QzYAAAAAzEwNQAAAAAD/P0M2AAAAAP8/nswAAAAAzEz/vwAAAAAys/+/AAAAADKzDUAAAAAA/7+ezAAAAAAyU2ZG/38AAMysZkb/fwAAMlOYuf9/AADMrJi5/38AADJTmLlYYQAAMlNmRlhhAAAyU5i5pp4AADJTZkamngAAzKyYuVhhAADMrJi5pp4AAMysZkZYYQAAzKxmRqaeAADMTA1AWGEAAMxM/79YYQAAzEwNQKaeAADMTP+/pp4AADKz/79YYQAAMrP/v6aeAAAysw1App4AADKzDUBYYQAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_f080v"]
resource_name = "WallWindowOpenUV_WallWindowOpenUV"
_surfaces = [{
"aabb": AABB(-1, -0.00292158, -1.2, 2, 2, 0.4),
"attribute_data": PackedByteArray("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"),
"format": 34896613399,
"index_count": 720,
"index_data": PackedByteArray("QQAfABsAQQA+AB8AXAAjACcAXABfACMAtAAAAKMAtABCAAAAcwAdAHYAcwAZAB0A+AAHAFIA+ADcAAcA8wAaAOcA8wBAABoA2QAQAKcA2QCaABAAwwAmAKsAwwBbACYAzAABAKkAzAB4AAEAngARAJsAngAVABEABAEEAOUABAFwAAQAEQEMAOMAEQGTAAwA9gAvAOoA9gBNAC8AuQAoAKwAuQBHACgATwAuADEATwBMAC4AvgA3ALMAvgBUADcA/QA1AGMA/QDtADUAVgA7ADgAVgBZADsA6wA/APIA6wAwAD8ArQBDALUArQApAEMAMQA+AEEAMQAuAD4ADwBMAE8ADwALAEwAoABGALgAoAACAEYA5ABOAPcA5AAOAE4AEwBZAFYAEwAXAFkAqABVAL8AqAASAFUA7ABTADQA7AD5AFMA3wBiAAUA3wD8AGIAsgBaAMIAsgA2AFoAOABfAFwAOAA7AF8ACwFmAAEBCwF7AGYAywBlAMcAywBvAGUAfQBrAIAAfQBoAGsAowBuAMgAowAAAG4AAwFxAAcBAwFnAHEAaAB2AGsAaABzAHYAIQCAACUAIQB9AIAA3wB6AAgB3wAFAHoAxQB5AM8AxQBkAHkAFgGKAAwBFgGfAIoAlACGAJEAlACJAIYA1gCIANIA1gCSAIgAoQCQANUAoQAIAJAADQCRAAkADQCUAJEADgGVABIBDgGLAJUA3QCdABUB3QAUAJ0AiQCbAIYAiQCeAJsA0ACcANoA0ACHAJwAggDbAJcAggDRANsAAgDUAIwAAgCgANQAjQDTAIMAjQDXANMAbADOAIEAbADEAM4AHADJAHUAHACkAMkAdwDGAG0AdwDKAMYAMwDBAGEAMwCxAMEAAwC8AFAAAwCmALwACgC7AEsACgCiALsALQC2ADwALQCvALYAUQCwADIAUQC9ALAASgCuACwASgC6AK4AfwCqACQAfwDNAKoAYACpAAEAYADAAKkAlgCmAAMAlgDYAKYAPQClAB4APQC3AKUABwAUAZgABwDcABQBhQATAY8AhQAPARMBmQANAYQAmQAXAQ0BIAAJAXwAIADgAAkBagAGAXQAagACAQYBfgAAAWkAfgAKAQABIgD/AOEAIgBeAP8AOgD6AO8AOgBXAPoABgD0AEgABgDiAPQAKwDxAEUAKwDpAPEAXQDuAP4AXQA5AO4ASQDoACoASQD1AOgAjgDiAAYAjgAQAeIAcgDmABgAcgAFAeYARADlAAQARADwAOUAWADeAPsAWAAWAN4AewFsAWgBewGPAWwBgAFrAYQBgAFxAWsBRQFBAVkBRQFJAUEBWAFdAVUBWAFAAV0BggGiAX4BggGeAaIBGwEwATUBGwEkATABPAEhATkBPAEqASEBGgE4ASABGgE0ATgBLAEoAS4BLAEmASgBoAGcAaQBoAGYAZwBHgEcASIBHgEYARwBLQEfASMBLQEvAR8BMQErAT0BMQElASsBTAFEAVABTAFIAUQBYQFDAWUBYQFfAUMBMwFXATcBMwFbAVcBTQFUAVwBTQFRAVQBcgF/AYsBcgFvAX8BHQEpAScBHQEZASkBQgFmAWQBQgFKAWYBOwFHAT8BOwFTAUcBlAF0AYwBlAFuAXQBmwGrAaMBmwGnAasBcAGHAXcBcAFzAYcBcQF8AWoBcQF4AXwBbgGIAXQBbgGQAYgBRgEyAT4BRgFaATIBOgFWAVIBOgE2AVYBrAG0AbABrAGoAbQBYAFOAV4BYAFiAU4BeQGxAY0BeQGVAbEBjgGmAYYBjgGyAaYBkgGdAYEBkgGuAZ0BqQGRAYkBqQGtAZEBmQGFAaUBmQF1AYUBqgF9AaEBqgGKAX0BmgF6AXYBmgGWAXoBaQGTAYMBaQFtAZMBZwFPAWMBZwFLAU8BnwGzAZcBnwGvAbMB"),
"material": ExtResource("1_h0jrj"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 437,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_sl3vx")

[sub_resource type="BoxShape3D" id="BoxShape3D_hv2fu"]
size = Vector3(2, 2, 0.400562)

[node name="WallWindoOpen" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_f080v")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00026238, 1, -1)
shape = SubResource("BoxShape3D_hv2fu")
