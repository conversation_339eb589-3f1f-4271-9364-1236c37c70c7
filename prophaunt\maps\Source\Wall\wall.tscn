[gd_scene load_steps=5 format=4 uid="uid://lu81xqf3o6wu"]

[ext_resource type="Material" uid="uid://lhnii45g7kv1" path="res://prophaunt/Mat/DarkLightBrown.tres" id="1_52av6"]

[sub_resource type="ArrayMesh" id="ArrayMesh_qx3ud"]
_surfaces = [{
"aabb": AABB(-1, 2.12392e-05, -1.1, 2, 2, 0.2),
"format": 34896613377,
"index_count": 480,
"index_data": PackedByteArray("DgAsAAcADgAoACwAKwANACYAKwAHAA0AEwAwABEAEwAtADAASwAqACQASwBIACoAQQAsACgAQQA+ACwAOQANACcAOQA3AA0AHwAMAB4AHwALAAwAHAAKABoAHAAJAAoANQAJABwANQAxAAkAEAAqAAgAEAAkACoAPwAtABMAPwBDAC0AGAAvABUAGAAuAC8ASQAvABYASQBNAC8AKQAPACIAKQAIAA8AOwAPACMAOwA4AA8AMwAKABkAMwAyAAoACAAjAA8ACAAqACMABwAnAA0ABwAsACcAQwAUAC0AQwBAABQALgAWAC8ALgAXABYALQASADAALQAUABIATQAVAC8ATQBKABUASAAjACoASABMACMAPgAnACwAPgBCACcANwAmAA0ANwA6ACYAOAAiAA8AOAA8ACIAMgAaAAoAMgA0ABoACwAdAAwACwAgAB0ACQAZAAoACQAbABkAMQAbAAkAMQA2ABsARwASAAQARwBCABIABQASABQABQAEABIARgAiADwARgBAACIARAAaADQARAAAABoAUQAmADoAUQBKACYAAgAWABcAAgADABYATwAbADYATwADABsAUAAYAAYAUABMABgAAgAbAAMAAgAZABsAPQAgAAYAPQAdACAAIQAIACkAIQAQAAgAJQAHACsAJQAOAAcASwACABcASwBOAAIASQA2ACUASQBPADYAPwA0ACEAPwBEADQAEwABAAAAEwARAAEAQQABABEAQQBFAAEADgA2ADEADgAlADYAEAA0ADIAEAAhADQAJAAyADMAJAAQADIAKAAxADUAKAAOADEAGAA9AAYAGAAVAD0APQA6AB0APQBRADoABQA8AB8ABQBGADwACwA8ADgACwAfADwADAA6ADcADAAdADoAIAA4ADsAIAALADgAHgA3ADkAHgAMADcAHgBHAAQAHgA5AEcAKABFAEEAKAA1AEUAEwBEAD8AEwAAAEQAHgAFAB8AHgAEAAUAGgABABwAGgAAAAEABQBAAEYABQAUAEAANQABAEUANQAcAAEAOQBCAEcAOQAnAEIAMABCAD4AMAASAEIAKQBAAEMAKQAiAEAAIQBDAD8AIQApAEMAEQA+AEEAEQAwAD4AIABQAAYAIAA7AFAAFgBPAEkAFgADAE8AJABOAEsAJAAzAE4AMwACAE4AMwAZAAIAOwBMAFAAOwAjAEwAPQBKAFEAPQAVAEoALgBMAEgALgAYAEwAKwBKAE0AKwAmAEoAJQBNAEkAJQArAE0AFwBIAEsAFwAuAEgA"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 82,
"vertex_data": PackedByteArray("//8AAKjmAAD//wAAVhkAAAAAAACo5gAAAAAAAFYZAAD/////VhkAAP////+o5gAAAAD//6jmAAD/f/9/VhkAAP9//3+o5gAA/38AAFYZAAD/fwAAqOYAAP9///+o5gAA/3///1YZAAD/f46CVhkAAP9/cH1WGQAA/3+OgqjmAAD/f3B9qOYAAP//cH1WGQAA//+OglYZAAD//3B9qOYAAP//joKo5gAAAACOglYZAAAAAHB9VhkAAAAAcH2o5gAAAACOgqjmAABwfQAAqOYAAI6CAACo5gAAcH0AAFYZAACOggAAVhkAAHB9//9WGQAAjoL//1YZAACOgv//qOYAAHB9//+o5gAAjoJwff7/AACOgo6C/v8AAHB9joL+/wAAcH1wff7/AABwfXB9AAAAAHB9joIAAAAAjoKOggAAAACOgnB9AAAAAI6C/3+o5gAAcH3/f6jmAABwff9/VhkAAI6C/39WGQAA////f6jmAAAAAP9/qOYAAAAA/39WGQAA////f1YZAAD/f48CVhkAAP9/jwKo5gAAcH2PAv//AACOgo8C//8AAI6CjwIAAAAAcH2PAgAAAAD/f2/9VhkAAP9/b/2o5gAAjoJv/QAAAABwfW/9AAAAAHB9b/3+/wAAjoJv/f7/AAAAAMX/VhkAAG/9/39WGQAAb/1wff7/AABv/Y6C/v8AAG/9cH0AAAAAb/2OggAAAABv/f9/qOYAAG/9jwL//wAAb/2PAgAAAABv/W/9/v8AAG/9b/0AAAAAjwL/f6jmAACPAnB9AAAAAI8CjoIAAAAAjwJwff7/AACPAo6C/v8AAI8C/39WGQAAjwKPAv//AACPAo8CAAAAAI8Cb/3+/wAAjwJv/QAAAAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_sqcjv"]
resource_name = "Wall_WallUV"
_surfaces = [{
"aabb": AABB(-1, 2.12392e-05, -1.1, 2, 2, 0.2),
"attribute_data": PackedByteArray("0iCFo9IghaPSIIWj0iCFo84gYaXOIGGlziBhpc4gYaW/Aq6ivwKuoo91AqSPdQKktQIqpbUCKqWEdXKmhHVypmI8P6ViPD+lYjw/pWI8P6VmPGOjZjxjo2Y8Y6NmPGOj7lfpo+5X6aPuV+mj7lfpozwv1L48L9S+PC/Uvjwv1L46L6m/Oi+pvzovqb86L6m/+xKNpfsSjaX7Eo2lCxPRogsT0aILE9GiNUozozVKM6M1SjOjJUr1pSVK9aUlSvWliy/Vvosv1b6LL9W+iy/Vvuwu077sLtO+7C7Tvuwu076JL6m/iS+pv4kvqb+JL6m/6i6ov+ouqL/qLqi/6i6ov0ouxqVKLsalSi7Gpe4uxaXuLsWl7i7FpUUu/6JFLv+iRS7/ouou/qLqLv6i6i7+ojdlTaY3ZU2mN2VNptxlTqbcZU6m3GVOpuZlnaPmZZ2j5mWdo0FlmqNBZZqjQWWao7cSzqK3Es6itxLOol8T0aJfE9GiXxPRoqUSjaWlEo2lpRKNpU4TkaVOE5GlThORpXdK+aV3Svmld0r5pdJJ9qXSSfal0kn2peNJL6PjSS+j40kvo4hKM6OISjOjiEozo4uMbA9nScGt3C4SwNwuEsCLjGZzZUmTrpUvFMCVLxTAhfBmc0cSIOaXLz+/ly8/v4XwbA9JEk7l3i4+v94uPr9uPgI8QxLF594uPr/eLj6/yTwGQ0US8+aXLz+/ly8/v0RBXT5JIEHmmS9rvpkva77GCKpxRyAT5+Auab7gLmm+ZkkqrmZJKq45L/G/OS/xv0gSt+VIErflOi9gvzovYL9EElznRBJc5zsvHb87Lx2/SCCq5kggquY8L4y+PC+MvpcuAaOXLgGjly4Bo5JlnaOSZZ2jkmWdo4llTKaJZUymiWVMpp0uw6WdLsOlnS7DpfsS26XBILC++xLbpcEgsL4OE4SivyCEvw4ThKK/IIS/hfBpQasSW6KrEluiwCAiv4uMaUFxE2eicRNnor4g7r/GCAI8XBP7pVwT+6XCIE6+bj6qcZUSAKaVEgCmwCAavyNKQaa2Pfi+I0pBprY9+L42SuiitD3NvzZK6KK0Pc2/REG1CMJJX6bCSV+mtz2Pvsk8XQ2DSmOmg0pjprU9W7+F8GlBmErKophKyqK1PWO/i4xpQddJxqLXScaisz0vwP9XwqX/V8Kl/1fCpf9XwqVGEonmRhKJ5p0uDqadLg6miL5sD2U7oa02LpaiNi6Wooi+ZnNjO3Ou9y6UovculKJuPqpxRRLz5jwuL6Y8Li+mnAtdPkcSIOb+Li6m/i4upmQ7Cq5kOwquli61opYutaKIvmlB0iAko9IgJKNuPgI8zSDCpc0gwqWIvmlBZjwCo2Y8AqOcC7UIYTygpWE8oKVKINflSiDX5ZJlUaOSZVGjFnQCPEUg5efoZb+m6GW/pnFyBkNHIBPnJ2W1pidltaaIvmwPSyBu5fVlLKP1ZSyjiL5mc0kgQeYyZTGjMmUxo0YgfOdGIHznh2WYpodlmKaIvmlBEAM+okR1kqMWdKpxAgOcpTR14KaIvmlB71eJo+9XiaNxcl0N+VcgpvlXIKY="),
"format": 34896613399,
"index_count": 480,
"index_data": PackedByteArray("NgCeAB4ANgCOAJ4AmwAwAIYAmwAdADAASACrAEIASACiAKsAEQGUAH0AEQEEAZQA7QCcAI0A7QDgAJwAzwAzAIsAzwDHADMAawAtAGgAawAqAC0AYgAnAFwAYgAkACcAvgAmAGMAvgCuACYAPACWACAAPAB+AJYA5gCgAEYA5gD2AKAAVwCoAE4AVwClAKgACgGmAE8ACgEaAaYAkwA6AHYAkwAjADoA1wA5AHsA1wDJADkAtgAoAFoAtgCwACgAIQB6ADgAIQCXAHoAHwCKADIAHwCfAIoA9wBJAKEA9wDqAEkApQBRAKgApQBUAFEAogBFAKsAogBLAEUAGwFMAKcAGwEOAUwABQF5AJUABQEVAXkA4QCJAJ0A4QDxAIkAxQCHADEAxQDTAIcAywB3ADsAywDbAHcAsgBdACkAsgC6AF0AKgBlAC0AKgBuAGUAJABZACcAJABfAFkArABgACUArADCAGAAAwFEABIAAwHzAEQAFwBFAEsAFwATAEUA/gB0ANgA/gDoAHQA+QBbALkA+QAAAFsAJQGEANAAJQEMAYQACwBRAFQACwAPAFEAIAFeAMEAIAEMAF4AJAFWABoAJAEXAVYACQBfAA0ACQBZAF8A3QBuABkA3QBlAG4AcgAiAJIAcgA+ACIAggAcAJoAggA0ABwAEwEKAFMAEwEeAQoACAHAAIAACAEfAcAA5AC4AHAA5AD4ALgASAAHAAMASABCAAcA7wAGAEEA7wD9AAYANQDDAK0ANQCDAMMAPwC7ALMAPwBzALsAfwCxALcAfwA9ALEAjwCvAL8AjwA3AK8AVwDfABsAVwBOAN8A3ADRAGQA3AAmAdEAFADZAGoAFAD/ANkALADaAMoALABsANoALgDSAMQALgBmANIAbwDIANYAbwArAMgAaQDGAM4AaQAvAMYAZwACARAAZwDNAAIBjAD7AOwAjAC8APsARwD6AOcARwACAPoAaAAVAGsAaAARABUAXAAFAGIAXAABAAUAFgDrAAABFgBKAOsAvQAEAPwAvQBhAAQAzADwAAEBzACIAPAAqgDyAOMAqgBDAPIAkQDpAPUAkQB1AOkAcQD0AOUAcQCQAPQAQADiAO4AQACpAOIAbQAjARgAbQDVACMBUAAhAQsBUAAOACEBfAAcARABfAC0ABwBtQAIAB0BtQBYAAgA1AAUASIB1AB4ABQB3gAPAScB3gBNAA8BpAAWAQcBpABVABYBmQANARkBmQCFAA0BgQAYAQkBgQCYABgBUgAGARIBUgCjAAYB"),
"material": ExtResource("1_52av6"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 296,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_qx3ud")

[sub_resource type="BoxShape3D" id="BoxShape3D_37ftd"]
size = Vector3(2, 2, 0.2)

[node name="Wall" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_sqcjv")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2.98023e-08, 1, -1)
shape = SubResource("BoxShape3D_37ftd")
