class_name RifleShootRunState
extends Node

@onready var character: Character = $"../.."

var counter = 0
var total_time = 0
var has_shot = false
var shot_time = 0

func _ready() -> void:
	pass

func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return
	
	counter += delta
	
	# Handle shooting at the right time in animation
	if counter >= shot_time and has_shot == false:
		has_shot = true
		on_shot_fired()

	var input_dir
	if counter >= total_time:
		# Shooting animation finished, return to appropriate rifle state based on movement
		input_dir = character.player_client_input(delta)
		if input_dir != Vector2(0, 0):
			character.state = character.State.RIFLE_RUN
		else:
			character.state = character.State.RIFLE_IDLE
		return
	
	var just_jumped = character.player_client_jump()
	input_dir = character.player_client_input(delta)
	
	# Allow jumping during shoot run
	if just_jumped:
		character.state = character.State.RIFLE_JUMP
		return
	
	# If player stops moving, switch to stationary shoot
	if input_dir == Vector2(0, 0):
		character.state = character.State.RIFLE_SHOOT
		return
	
	# Check for switching back to normal states (e.g., when weapon is holstered)
	if Input.is_action_just_pressed("holster_weapon"):
		character.state = character.State.STATE_RUN
		return
	
	# Handle movement during shooting (similar to rifle_run but with shooting animation)
	#handle_movement_during_shoot(input_dir)
	character.handle_rifle_mode(delta, true)

func handle_movement_during_shoot(input_dir):
	# Apply movement while shooting (reduced speed for accuracy)
	var shoot_run_speed = character.SPEED * 0.6  # Shooting while running is slower
	
	if character.camera_controller or character.is_bot():
		var look_at_pos = null
		if character.controls == Constants.Controls.Bot:
			look_at_pos = Vector3(character.global_position.x + input_dir.x, character.global_position.y, character.global_position.z + input_dir.y)
		else:
			look_at_pos = character.camera_controller.get_node("LookAt").global_position
		
		var pointer = character.get_tree().get_first_node_in_group("pointer")
		if pointer == null:
			return
		
		var normal = input_dir.normalized()
		if character.controls == Constants.Controls.Bot:
			if input_dir == Vector2(0, 0):
				normal = Vector2(0, 0)
			else:
				normal = Vector2(1, 0)
		
		var straight = Vector3(look_at_pos.x - character.global_position.x, 0, look_at_pos.z - character.global_position.z).normalized()
		var length = 5
		var pointer_target = Vector3(-normal.x * length, 0, -normal.y * length)
		var angle : float = Vector3(0, 0, 1).angle_to(straight)
		if angle > 0.001 or angle < -0.001:
			var axis_vect = Vector3(0, 0, 1).cross(straight)
			if axis_vect != Vector3(0, 0, 0):
				pointer_target = pointer_target.rotated(axis_vect.normalized(), angle)
		pointer.global_position = pointer_target + character.global_position
		
		if (pointer.global_position - character.global_position).length() > 0.01:
			var lerp_direction = pointer.global_position
			character.look_at(lerp_direction)
			character.last_direction = (lerp_direction - character.global_position).normalized()
		
		var direction = (pointer.global_position - character.global_position).normalized()
		
		# Apply movement
		if Constants.is_server or character.should_move_in_client():
			character.velocity.x = direction.x * shoot_run_speed
			character.velocity.z = direction.z * shoot_run_speed

func start_state():
	if character.state == character.State.RIFLE_SHOOT_RUN:
		return
	
	character.state = character.State.RIFLE_SHOOT_RUN
	counter = 0
	has_shot = false
	
	# Get shoot run animation length
	var animation_name = character.RIFLE_SHOOT_RUN_ANIMATION
	if character.animation_player.has_animation(animation_name):
		total_time = character.animation_player.get_animation(animation_name).length
		# Set shot time to be partway through animation (adjust as needed)
		shot_time = total_time * 0.3  # Fire at 30% through animation
	else:
		total_time = 0.6  # Default shoot run time (slightly longer than stationary)
		shot_time = 0.18  # Default shot timing
	
	# Animation will be handled in handle_animation function

func end_state():
	counter = 0
	has_shot = false

func on_shot_fired():
	# This can be connected to shooting logic (e.g., spawn bullet, reduce ammo)
	print("Rifle shot fired while running!")
	# You can emit a signal here or call shooting logic
	# Example: character.emit_signal("rifle_shot_fired")
	
	# Add muzzle flash, sound effects, etc. here
	if Constants.is_client() and character.controls == Constants.Controls.Player:
		# Play shooting sound
		# SoundManager.play_rifle_shot_sound()
		pass
