extends Control
class_name ProphauntLobbyScene

# Prophaunt Lobby Scene
# Displays character, weapon, and abilities before starting a Prophaunt game

# UI References
@onready var character_preview_image: TextureRect = $CharacterPreviewImage
@onready var character_preview:CharacterPreview = $CharacterContainer/CharacterPreview
@onready var weapon_icon = $WeaponPanel/WeaponContainer/WeaponIcon
@onready var weapon_selector_button = $WeaponPanel/WeaponSelectorButton
@onready var grenades_count = $AbilitiesPanel/AbilitiesContainer/GrenadesContainer/GrenadesCount
@onready var hexes_count = $AbilitiesPanel/AbilitiesContainer/HexesContainer/HexesCount
@onready var smart_label = $TopPanel/CurrencyPanel/SmartShow/SmartLabel
@onready var loading_panel = $LoadingPanel
@onready var http_request = $HTTPRequest
@onready var weapon_selector = $WeaponSelector

# Player data
var current_weapon_icon: Texture2D
var selected_weapon_id: int = 15  # Default pistol
var grenades_available: int = 0
var hexes_available: int = 0

func _ready():
	# Set game mode
	Selector.selected_game_mode = Constants.GameMode.Prophaunt

	# Initialize the lobby
	setup_ui()
	load_character_preview()
	fetch_item_data()


func setup_ui():
	"""Initialize UI elements"""
	# Update currency displays
	smart_label.text = str(Selector.my_smart)

	# Set default values
	grenades_count.text = str(grenades_available)
	hexes_count.text = str(hexes_available)

	# Load previously selected weapon
	selected_weapon_id = DataSaver.get_item("prophaunt_selected_weapon", 15)  # Default pistol
	load_selected_weapon()

	# Hide loading panel initially
	loading_panel.visible = false


func load_character_preview():
	"""Load and display the selected character"""
	character_preview_image.camera_changed.connect(character_preview.on_camera_changed)
	character_preview_image.preview = character_preview
	character_preview.update(Selector.selected_character)
	character_preview.model.rifleIdleState.start_state()
	character_preview.model.handle_animation()


func fetch_item_data():
	"""Fetch current items from backend"""
	show_loading(true)
	
	var url = Constants.BACKEND_URL + "/prophunt/item_data/client/"
	var data = {}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	
	http_request.cancel_request()
	http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func _on_http_request_completed(_result: int, response_code: int, _headers: PackedStringArray, body: PackedByteArray):
	"""Handle HTTP request completion"""
	show_loading(false)
	
	if response_code == 200:
		var json_response = JSON.parse_string(body.get_string_from_utf8())
		if json_response:
			handle_item_data_response(json_response["item_data"])
		else:
			print("ProphauntLobby: Failed to parse JSON response")
			set_default_item_values()
	else:
		print("ProphauntLobby: HTTP request failed with code: ", response_code)
		set_default_item_values()


func handle_item_data_response(data: Dictionary):
	"""Process the item data response from backend"""
	# Extract weapon information
	if data.has("weapon"):
		var weapon_data = data["weapon"]
		if weapon_data.has("icon_path"):
			load_weapon_icon(weapon_data["icon_path"])
	
	# Extract grenades count
	if data.has("grenades"):
		grenades_available = data["grenades"]
		grenades_count.text = str(grenades_available)
	
	# Extract hexes count
	if data.has("hexes"):
		hexes_available = data["hexes"]
		hexes_count.text = str(hexes_available)
	
	# Update smart points if provided
	if data.has("smart"):
		Selector.my_smart = data["smart"]
		smart_label.text = str(Selector.my_smart)


func load_weapon_icon(icon_path: String):
	"""Load weapon icon from path"""
	if icon_path.is_empty():
		return

	var texture = load(icon_path) as Texture2D
	if texture:
		weapon_icon.texture = texture
		current_weapon_icon = texture
		print("ProphauntLobby: Weapon icon loaded from: ", icon_path)
	else:
		print("ProphauntLobby: Failed to load weapon icon from: ", icon_path)


func load_selected_weapon():
	"""Load the selected weapon icon"""
	var weapon_item = InventoryManager.get_item_by_id(selected_weapon_id) as GunInventoryItem
	if weapon_item and weapon_item.icon:
		current_weapon_icon = weapon_item.icon
		weapon_icon.texture = current_weapon_icon
		print("ProphauntLobby: Selected weapon loaded: ", weapon_item.name)
	else:
		# Fallback to default weapon
		var default_weapon_path = "res://Scenes/ui/assets/pistol.png"
		if ResourceLoader.exists(default_weapon_path):
			load_weapon_icon(default_weapon_path)


func set_default_item_values():
	"""Set default values when backend request fails"""
	grenades_available = 3  # Default grenade count
	hexes_available = 2     # Default hex count
	
	grenades_count.text = str(grenades_available)
	hexes_count.text = str(hexes_available)
	
	# Load default weapon icon if available
	var default_weapon_path = "res://Scenes/ui/assets/pistol.png"
	if ResourceLoader.exists(default_weapon_path):
		load_weapon_icon(default_weapon_path)
	
	print("ProphauntLobby: Set default item values")


func show_loading(is_show: bool):
	"""Show or hide loading panel"""
	loading_panel.visible = is_show


func _on_back_button_pressed():
	"""Handle back button press - return to main menu"""
	print("ProphauntLobby: Back button pressed")
	SoundManager.play_click_sound()
	get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")


func _on_start_button_pressed():
	"""Handle start button press - go to ProphauntGame"""
	print("ProphauntLobby: Start button pressed")
	SoundManager.play_click_sound()

	# Save player name and selected weapon before starting
	DataSaver.set_item("handle", Selector.my_name, false)
	DataSaver.set_item("prophaunt_selected_weapon", selected_weapon_id, false)
	DataSaver.send_save_request()

	# Transition to ProphauntGame scene
	get_tree().change_scene_to_file("res://prophaunt/scenes/ProphauntGame.tscn")


func _on_weapon_selector_button_pressed():
	"""Handle weapon selector button press - show weapon selector"""
	print("ProphauntLobby: Weapon selector button pressed")
	SoundManager.play_click_sound()
	weapon_selector.show_selector()


func _on_weapon_selected(weapon_id: int, selected_weapon_icon: Texture2D):
	"""Handle weapon selection from weapon selector"""
	print("ProphauntLobby: Weapon selected: ", weapon_id)
	selected_weapon_id = weapon_id
	current_weapon_icon = selected_weapon_icon

	# Update weapon panel display
	update_weapon_panel()


func update_weapon_panel():
	"""Update the weapon panel with selected weapon"""
	if current_weapon_icon:
		weapon_icon.texture = current_weapon_icon

	# Save selected weapon
	DataSaver.set_item("prophaunt_selected_weapon", selected_weapon_id, false)


func _process(_delta):
	"""Update UI elements that need constant refresh"""
	# Update smart points display (in case it changes)
	smart_label.text = str(Selector.my_smart)


func _notification(what: int) -> void:
	"""Handle system notifications"""
	if what == NOTIFICATION_WM_CLOSE_REQUEST or what == NOTIFICATION_WM_GO_BACK_REQUEST:
		# Handle back button on mobile or window close
		_on_back_button_pressed()
