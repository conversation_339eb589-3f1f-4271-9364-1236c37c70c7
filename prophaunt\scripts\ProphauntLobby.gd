extends ProphauntServerState

var lobby_timer = 0
var lobby_update_counter = 0

func set_state():
	print("Set State to Prophaunt Lobby")
	if server.peer:
		server.peer.refuse_new_connections = false
	lobby_update_counter = 0
	lobby_timer = 0
	server.state = Constants.ServerState.Lobby
	lobby_timer = Constants.LOBBY_TIME


func run(delta):
	if not server:
		return

	# Check if we have enough players to start (minimum 2 for testing, ideally 4+)
	var min_players = 1  # Can be adjusted
	if server.count_players_ready() >= min_players:
		start_game()
		return

	lobby_timer -= delta
	if lobby_timer <= 0:
		if server.is_all_players_ready():
			start_game()
			return
		elif lobby_timer <= -3:
			start_game()
			return
	
	if server.count_non_bot_players() == 0:
		lobby_timer = Constants.LOBBY_TIME


	lobby_update_counter += delta
	if lobby_update_counter < Constants.SERVER_LOBBY_SYNC:
		return
	
	lobby_update_counter = 0
	
	# Send Prophaunt-specific lobby data
	var lobby_data = {
		"j": server.players_count,
		"s": min_players,
		"t": int(lobby_timer) + 1,
		#"round": current_round,
		#"rounds_on_map": rounds_played_on_map,
	}
	
	for key in server.players_data.keys():
		if server.is_bot(key):
			continue
		if server.is_dc(key):
			continue
		lobby_update.rpc_id(key, lobby_data)


func start_game():
	"""Start a new Prophaunt round"""
	print("Starting Prophaunt Game ")
	
	# Assign teams
	#assign_teams()
	
	# Initialize round
	round_timer = Constants.PROPHAUNT_ROUND_TIME
	round_start_time = Time.get_ticks_msec()
	
	# Spawn players
	#spawn_prophaunt_players()
	
	# Transition to InGame state
	server.set_state_to_prophaunt_ingame()


func on_new_player(_players_data):
	lobby_timer += Constants.SERVER_PLAYER_JOINED_ADD_LOBBY_TIME
	
	#Server
	for i in range(0, 5):
		Constants.client.game_scene.create_and_add_bot()


@rpc("unreliable", "authority", "call_remote", Constants.CHANNEL_LOBBY_UNRELIABLE)
func lobby_update(data):
	if Constants.is_headless:
		return
	if ClientRPC.peer == null:
		return

	var client = Constants.client
	client.lobby_players_joined = data["j"]
	client.lobby_players_start = data["s"]
	client.lobby_timer = data["t"]
