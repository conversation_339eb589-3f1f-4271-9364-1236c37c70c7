extends ProphauntServerState

var game_update_counter = 0.0
var round_end_timer = 0.0
var round_ended = false
var round_manager: ProphauntRoundManager
var audio_system: ProphauntAudioSystem

func set_state():
	print("Set State to Prophaunt InGame")
	server.state = Constants.ServerState.InGame
	round_ended = false
	round_end_timer = 0.0

	# Initialize round manager
	if not round_manager:
		round_manager = ProphauntRoundManager.new()
		round_manager.name = "RoundManager"
		add_child(round_manager)

		# Connect round manager signals
		round_manager.round_ended.connect(_on_round_ended)
		round_manager.timer_updated.connect(_on_timer_updated)
		round_manager.props_eliminated.connect(_on_props_eliminated)
		round_manager.round_warning.connect(_on_round_warning)

	# Initialize audio system
	if not audio_system:
		audio_system = ProphauntAudioSystem.new()
		audio_system.name = "AudioSystem"
		add_child(audio_system)

		# Start prop detection timers
		#for prop_id in props_alive:
			#audio_system.start_prop_detection_timer(prop_id)
	
	start_round()


func start_round():
	round_timer = Constants.PROPHAUNT_ROUND_TIME
	round_start_time = Time.get_ticks_msec()
	
	assign_teams()
	for player_id in server.players_data.keys():
		if server.is_bot(player_id):
			continue
		var player_data = server.players_data[player_id]
		assign_my_team.rpc_id(player_id, player_data["prophaunt_team"])
	

	spawn_prophaunt_players()
	# Start the round
	round_manager.start_round(current_round, server.props_team, server.haunters_team)
	# Notify all players that the round has started
	for key in server.players_data.keys():
		if server.is_bot(key):
			continue
		if server.is_dc(key):
			continue
		prophaunt_round_start.rpc_id(key, current_round, round_manager.get_time_remaining())


func spawn_prophaunt_players():
	"""Spawn players in their appropriate locations"""
	var haunter_spawn_index = 0
	var prop_spawn_index = 0
	
	for key in server.players_data.keys():
		if server.is_dc(key):
			continue
		
		var player:Character = server.players[key]
		if not player:
			continue
		
		var player_manager = ProphauntPlayerManager.get_instance()
		if server.players_data[key]["prophaunt_team"] == Constants.ProphauntTeam.HAUNTERS:
			# Spawn haunters in haunter room
			var pos = get_haunter_spawn_position(haunter_spawn_index)
			var rot = get_haunter_spawn_rotation(haunter_spawn_index)
			player.global_position = pos
			player.global_rotation = rot
			if not server.is_bot(key):
				player.force_set_pos_rot.rpc_id(key, pos, rot)

			# Initialize ProphauntPlayer component for haunter
			player_manager.initialize_prophaunt_player(key, player, Constants.ProphauntTeam.HAUNTERS)

			haunter_spawn_index += 1
		else:
			# Spawn props randomly as objects in the map
			player_manager.initialize_prophaunt_player(key, player, Constants.ProphauntTeam.PROPS)
			assign_random_prop_disguise(key)
			var pos = get_prop_spawn_position(prop_spawn_index)
			var rot = get_prop_spawn_rotation(prop_spawn_index)
			player.global_position = pos
			player.global_rotation = rot
			# Assign random prop disguise
			prop_spawn_index += 1
		
		# Update player data
		server.players_data[key]["server"]["checkpoint"] = player.global_position
		server.players_data[key]["d"]["p"] = player.global_position


func get_haunter_spawn_position(index):
	"""Get spawn position for haunters (in haunter room)"""

	var map: PropHauntMap = Constants.client.game_scene.selected_map
	var count = map.haunter_spawn.get_child_count()
	return map.haunter_spawn.get_child(index % count).global_position


func get_haunter_spawn_rotation(index):
	"""Get spawn rotation for haunters"""
	var map: PropHauntMap = Constants.client.game_scene.selected_map
	var count = map.haunter_spawn.get_child_count()
	return map.haunter_spawn.get_child(index % count).global_rotation


func get_prop_spawn_position(index):
	"""Get spawn position for props (scattered around map)"""
	var map_manager = get_node_or_null("MapManager")
	if map_manager:
		return map_manager.get_prop_spawn_point(index)

	# Fallback positions
	var positions = [
		Vector3(5, 1, 5),
		Vector3(-5, 1, 5),
		Vector3(5, 1, -5),
		Vector3(-5, 1, -5),
		Vector3(10, 1, 0),
		Vector3(-10, 1, 0),
		Vector3(0, 1, 10),
		Vector3(0, 1, -10)
	]
	return positions[index % positions.size()]


func get_prop_spawn_rotation(_index):
	"""Get spawn rotation for props"""
	return Vector3(0, randf() * TAU, 0)  # Random Y rotation


func run(delta):
	if not server:
		return
	
	# Update basic server functionality
	server.set_all_player_scenes_data()
	server.update_players_data()
	
	if not round_ended:
		# Update round manager
		if round_manager:
			round_manager.update_round(delta)

		# Update cooldowns and effects
		update_cooldowns(delta)
		update_hex_effects(delta)
		
		# Send game updates to clients
		game_update_counter += delta
		if game_update_counter >= 0.1:  # Update 10 times per second
			send_game_update()
			game_update_counter = 0.0
	else:
		# Handle round end transition
		round_end_timer += delta
		if round_end_timer >= 5.0:  # 5 seconds to show results
			transition_to_next_round()
	
	# Handle network sync
	if server.is_syncv2():
		server.handle_individual_sync(delta)
		server.serverSyncV2.run(delta)
	else:
		server.handle_individual_sync(delta)
		server.handle_send_all_players_data(delta)


func end_round(winner_team):
	"""End the current round"""
	print("Round ended! Winner: ", "Props" if winner_team == Constants.ProphauntTeam.PROPS else "Haunters")
	round_ended = true
	round_end_timer = 0.0
	
	# Calculate scores and stats
	var round_results = calculate_round_results(winner_team)
	
	# Notify all players of round end
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		prophaunt_round_end.rpc_id(key, winner_team, round_results)


func calculate_round_results(winner_team):
	"""Calculate round results and player stats"""
	var results = {
		"winner_team": winner_team,
		"round_time": Constants.PROPHAUNT_ROUND_TIME - round_timer,
		#"props_survived": props_alive.size(),
		"total_props": server.props_team.size(),
		"total_haunters": server.haunters_team.size(),
		"player_stats": {}
	}
	
	# Calculate individual player stats
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		var player_stats = {
			"team": server.players_data[key]["prophaunt_team"],
			"survived": server.players_data[key]["prophaunt_state"] == Constants.ProphauntPlayerState.ALIVE,
			"hp_remaining": server.players_data[key]["prophaunt_hp"],
			"score": 0
		}
		
		# Award points based on performance
		if player_stats["team"] == Constants.ProphauntTeam.PROPS:
			if player_stats["survived"]:
				player_stats["score"] = 100  # Survival bonus
			else:
				player_stats["score"] = 25   # Participation
		else:  # Haunters
			if winner_team == Constants.ProphauntTeam.HAUNTERS:
				player_stats["score"] = 75   # Win bonus
			else:
				player_stats["score"] = 25   # Participation
		
		results["player_stats"][key] = player_stats
	
	return results


func transition_to_next_round():
	"""Transition to the next round or map"""
	rounds_played_on_map += 1
	current_round += 1
	
	if rounds_played_on_map >= Constants.PROPHAUNT_ROUNDS_PER_MAP:
		# Change map
		rounds_played_on_map = 0
		print("Changing to next map after ", Constants.PROPHAUNT_ROUNDS_PER_MAP, " rounds")
	
	# Reset player states for next round
	reset_player_states()
	
	# Go back to lobby for next round
	server.set_state_to_prophaunt_lobby()


func reset_player_states():
	"""Reset all player states for the next round"""
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		server.players_data[key]["prophaunt_hp"] = Constants.PROPHAUNT_PROP_DEFAULT_HP
		server.players_data[key]["prophaunt_state"] = Constants.ProphauntPlayerState.ALIVE
		server.players_data[key]["prophaunt_hex_cooldown"] = 0.0
		server.players_data[key]["prophaunt_sound_cooldown"] = Constants.PROPHAUNT_PROP_SOUND_COOLDOWN
		server.players_data[key]["prophaunt_disguise"] = ""


func send_game_update():
	"""Send game state update to all clients"""
	var game_data = {
		"round_timer": round_timer,
		#"props_alive": props_alive.size(),
		"total_props": server.props_team.size(),
		"current_round": current_round,
		"player_states": {}
	}
	
	# Add player-specific data
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		game_data["player_states"][key] = {
			"hp": server.players_data[key]["prophaunt_hp"],
			"state": server.players_data[key]["prophaunt_state"],
			"team": server.players_data[key]["prophaunt_team"],
			"hex_cooldown": server.players_data[key]["prophaunt_hex_cooldown"]
		}
	
	# Send to all players
	var prophaunt_game:PropHauntGame = Constants.client.game_scene
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		prophaunt_game.prophaunt_game_update.rpc_id(key, game_data)


######################################Server RPCs
# RPC handlers for Prophaunt actions
@rpc("any_peer", "call_remote", "reliable")
func prophaunt_shoot(target_position: Vector3, damage: int):
	var shooter_id = multiplayer.get_remote_sender_id()
	
	# Verify shooter is a haunter and not hexed
	if server.players_data[shooter_id]["prophaunt_team"] != Constants.ProphauntTeam.HAUNTERS:
		return
	if server.players_data[shooter_id]["prophaunt_state"] == Constants.ProphauntPlayerState.HEXED:
		return
	
	# Check if shot hit a prop player
	var hit_player = find_player_at_position(target_position)
	
	if hit_player != -1 and server.players_data[hit_player]["prophaunt_team"] == Constants.ProphauntTeam.PROPS:
		# Hit a prop
		damage_prop(hit_player, damage)
	else:
		# Hit wrong target, damage shooter
		damage_prop(shooter_id, Constants.PROPHAUNT_SELF_DAMAGE)


@rpc("any_peer", "call_remote", "reliable")
func prophaunt_grenade(_center_position: Vector3):
	var thrower_id = multiplayer.get_remote_sender_id()
	
	# Verify thrower is a haunter and not hexed
	if server.players_data[thrower_id]["prophaunt_team"] != Constants.ProphauntTeam.HAUNTERS:
		return
	if server.players_data[thrower_id]["prophaunt_state"] == Constants.ProphauntPlayerState.HEXED:
		return
	
	# Damage all props in radius
	#for prop_id in props_alive:
		#var prop_position = server.players[prop_id].global_position
		#var distance = center_position.distance_to(prop_position)
		#
		#if distance <= Constants.PROPHAUNT_GRENADE_RADIUS:
			#damage_prop(prop_id, Constants.PROPHAUNT_GRENADE_DAMAGE)


@rpc("any_peer", "call_remote", "reliable")
func prophaunt_hex(_target_position: Vector3):
	var caster_id = multiplayer.get_remote_sender_id()
	
	# Verify caster is a prop and hex is off cooldown
	if server.players_data[caster_id]["prophaunt_team"] != Constants.ProphauntTeam.PROPS:
		return
	if server.players_data[caster_id]["prophaunt_hex_cooldown"] > 0:
		return
	
	# Find haunters in range
	var caster_position = server.players[caster_id].global_position
	for haunter_id in server.haunters_team:
		var haunter_position = server.players[haunter_id].global_position
		var distance = caster_position.distance_to(haunter_position)
		
		if distance <= 5.0:  # Hex range
			hex_haunter(haunter_id, Constants.PROPHAUNT_HEX_DURATION)
	
	# Set cooldown
	server.players_data[caster_id]["prophaunt_hex_cooldown"] = Constants.PROPHAUNT_HEX_COOLDOWN


@rpc("any_peer", "call_remote", "reliable")
func prophaunt_change_disguise(new_disguise: String):
	var player_id = multiplayer.get_remote_sender_id()
	
	# Verify player is a prop
	if server.players_data[player_id]["prophaunt_team"] != Constants.ProphauntTeam.PROPS:
		return
	
	server.players_data[player_id]["prophaunt_disguise"] = new_disguise
	
	# Notify all players
	for key in server.players_data.keys():
		if not server.is_bot(key) and not server.is_dc(key):
			multiplayer.rpc(key, ClientRPC, "prophaunt_player_disguised", [player_id, new_disguise])


######################################Client RPCs
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func prophaunt_round_start(round_index, time):
	Constants.hide_mouse()
	Constants.client.game_scene.prophaunt_round_start(round_index, time)


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func prophaunt_round_end(winner_team, data):
	Constants.client.game_scene.prophaunt_round_end(winner_team, data)


######################################Client RPCs
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func assign_my_team(team: Constants.ProphauntTeam):
	var my_player = Constants.client.game_scene.player
	print("my team is : ", "Haunter" if team == Constants.ProphauntTeam.HAUNTERS else "Prop")
	ProphauntPlayerManager.get_instance().initialize_prophaunt_player(multiplayer.get_unique_id(), my_player, team)


func find_player_at_position(position: Vector3):
	"""Find player at or near the given position"""
	var closest_player = -1
	var closest_distance = 2.0  # Maximum hit distance

	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue

		var player_position = server.players[key].global_position
		var distance = position.distance_to(player_position)

		if distance < closest_distance:
			closest_distance = distance
			closest_player = key

	return closest_player


# Round manager signal handlers
func _on_round_ended(winner: Constants.ProphauntTeam, _stats: Dictionary):
	"""Called when round ends"""
	end_round(winner)


func _on_timer_updated(time_remaining: float):
	"""Called when timer updates"""
	round_timer = time_remaining


func _on_props_eliminated(eliminated_count: int, total_count: int):
	"""Called when props are eliminated"""
	print("Props eliminated: ", total_count - eliminated_count, "/", total_count, " remaining")


func _on_round_warning(warning_type: String, time_remaining: float):
	"""Called for round time warnings"""
	print("Round warning: ", warning_type, " - ", time_remaining, " seconds remaining")

	# Notify all players of the warning
	for key in server.players_data.keys():
		if not server.is_bot(key) and not server.is_dc(key):
			Constants.client.game_scene.prophaunt_round_warning.rpc_id(key, warning_type, time_remaining)


func assign_random_prop_disguise(player_id):
	"""Assign a random prop disguise to a prop player"""
	# Get available props from map manager
	#server.players_data[player_id]["prophaunt_disguise"] = selected_prop

	# Initialize ProphauntPlayer component
	var character:Character = server.players[player_id]
	character.prophaunt_player.apply_random_disguise()
	
	# Notify all players of the disguise
	#var prophaunt_game:PropHauntGame = Constants.client.game_scene
	#for key in server.players_data.keys():
		#if not server.is_bot(key) and not server.is_dc(key):
			#var selected_prop = character.prophaunt_player.disguise_system.current_disguise.index
			#prophaunt_game.prophaunt_player_disguised.rpc_id(key, player_id, selected_prop)
