[gd_scene load_steps=4 format=3 uid="uid://cn1c31s7u8y15"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_744fn"]
[ext_resource type="PackedScene" uid="uid://crdogyitby6le" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/BasketCarrots.tscn" id="2_d5llm"]

[sub_resource type="BoxShape3D" id="BoxShape3D_fn0n5"]
size = Vector3(2.07758, 1.177, 2.0271)

[node name="CarrotBasketProp" instance=ExtResource("1_744fn")]

[node name="BasketCarrot" parent="Meshes" index="0" instance=ExtResource("2_d5llm")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.000152588, 0.57605, 0.00622559)
shape = SubResource("BoxShape3D_fn0n5")
