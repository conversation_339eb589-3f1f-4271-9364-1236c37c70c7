[gd_resource type="Resource" script_class="GunInventoryItem" load_steps=4 format=3 uid="uid://drp0m5q7x8jn0"]

[ext_resource type="Texture2D" uid="uid://pm05jfwruxpr" path="res://Scenes/ui/assets/pistol.png" id="1_blaster_d_icon"]
[ext_resource type="PackedScene" uid="uid://u245a3wk8k1w" path="res://prophaunt/assets/Guns/Scene/blaster_d.tscn" id="2_blaster_d_scene"]
[ext_resource type="Script" path="res://Inventory/GunInventoryItem.gd" id="3_blaster_d_script"]

[resource]
script = ExtResource("3_blaster_d_script")
damage = 90
ammo = 15
gun_type = "range"
range = 45
animation_speed = 3.0
scene = ExtResource("2_blaster_d_scene")
scale = Vector3(150, 150, 150)
position = Vector3(0, 0, 0)
rotation = Vector3(10, 125, 90)
ui = 2
id = 103
name = "BLASTER_D"
icon = ExtResource("1_blaster_d_icon")
price = 300
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
