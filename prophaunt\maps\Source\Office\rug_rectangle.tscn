[gd_scene load_steps=4 format=4 uid="uid://e0s3xtsa6rkw"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_1k278"]

[sub_resource type="ArrayMesh" id="ArrayMesh_gbe52"]
_surfaces = [{
"aabb": AABB(-0.785, -0.46, -0.01, 1.57, 0.92, 0.01),
"format": 34359742465,
"index_count": 84,
"index_data": PackedByteArray("AQACAAAAAgABAAMAAgAEAAAABAACAAUACAAJAAoACwAEAAUACwAFAAoACAACAAMAAgAIAAUADQAOAAwADgANAA8ADAAGAAsABgAMAAkACQAMAA4ACQAOAA8ACwANAAwADQALAAoADQAKAA8ADwAKAAkABgAHAAQABwAGAAgACAAGAAkABAALAAYACgAFAAgAAQAEAAcABAABAAAAAQAIAAMACAABAAcA"),
"lods": [0.480388, PackedByteArray("AQACAAAAAgABAAMAAgAEAAAABAACAAUACAAJAAoACgAFAAgACAALAAkACwAFAAoABwALAAgACwAEAAUACwAHAAQACAACAAMAAgAIAAUACwAPAAkADwAKAAkADwALAAoAAQAEAAcABAABAAAAAQAIAAMACAABAAcA")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("wfVIPx6F6z4AAICwwfVIPyCF674AAICwxfVIvx6F6z4AAICwxfVIvyCF674AAICwwfVIPx6F6z4L1yO8xfVIvx6F6z4L1yO84t0rP2JVsb4L1yO8wfVIPyCF674L1yO8xfVIvyCF674L1yO83d0rv2JVsb4L1yO83d0rv2BVsT4L1yO84t0rP2BVsT4L1yO869MYPw/Ig74I1yO869MYPwvIgz4O1yO869MYvw/Ig74I1yO869MYvwvIgz4O1yO8")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_mflhh"]
resource_name = "RugRectangle_rugRectangle"
_surfaces = [{
"aabb": AABB(-0.785, -0.46, -0.01, 1.57, 0.92, 0.01),
"attribute_data": PackedByteArray("MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+ScHkPojjlD4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPknB5D6I45Q+MGHrPqo0hT5JweQ+iOOUPjBh6z6qNIU+ScHkPojjlD4wYes+qjSFPknB5D6I45Q+MGHrPqo0hT5JweQ+iOOUPjBh6z6qNIU+ScHkPojjlD4wYes+qjSFPknB5D6I45Q+MGHrPqo0hT4="),
"format": 34359742487,
"index_count": 84,
"index_data": PackedByteArray("AwAGAAAABgADAAkABwANAAEADQAHABAAFwAbAB0AHwAMAA8AHwAPAB0AGQAIAAsACAAZABEAIwAlACEAJQAjACcAIAASAB4AEgAgABoAGgAgACQAGgAkACYAHgAiACAAIgAeABwAIgAcACYAJgAcABoAEwAUAAwAFAATABcAFwATABsADAAfABMAHQAPABcABQAOABYADgAFAAIABAAYAAoAGAAEABUA"),
"lods": [0.480388, PackedByteArray("AwAGAAAABgADAAkABwANAAEADQAHABAAFwAbAB0AHQAPABcAFwAfABsAHwAPAB0AFAAfABcAHwAMAA8AHwAUAAwAGQAIAAsACAAZABEAHgAmABoAJgAcABoAJgAeABwABQAOABYADgAFAAIABAAYAAoAGAAEABUA")],
"material": ExtResource("1_1k278"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("wfVIPx6F6z4AAICwwfVIPx6F6z4AAICwwfVIPx6F6z4AAICwwfVIPyCF674AAICwwfVIPyCF674AAICwwfVIPyCF674AAICwxfVIvx6F6z4AAICwxfVIvx6F6z4AAICwxfVIvx6F6z4AAICwxfVIvyCF674AAICwxfVIvyCF674AAICwxfVIvyCF674AAICwwfVIPx6F6z4L1yO8wfVIPx6F6z4L1yO8wfVIPx6F6z4L1yO8xfVIvx6F6z4L1yO8xfVIvx6F6z4L1yO8xfVIvx6F6z4L1yO84t0rP2JVsb4L1yO84t0rP2JVsb4L1yO8wfVIPyCF674L1yO8wfVIPyCF674L1yO8wfVIPyCF674L1yO8xfVIvyCF674L1yO8xfVIvyCF674L1yO8xfVIvyCF674L1yO83d0rv2JVsb4L1yO83d0rv2JVsb4L1yO83d0rv2BVsT4L1yO83d0rv2BVsT4L1yO84t0rP2BVsT4L1yO84t0rP2BVsT4L1yO869MYPw/Ig74I1yO869MYPw/Ig74I1yO869MYPwvIgz4O1yO869MYPwvIgz4O1yO869MYvw/Ig74I1yO869MYvw/Ig74I1yO869MYvwvIgz4O1yO869MYvwvIgz4O1yO8/3//f////z//f///////v////3////+//3//f////z//fwAA////v////3////+//3//f////z//f///////vwAA/3////+//3//f////z//fwAA////vwAA/3////+//////////7//f///////v////3////+//////////7//f///////vwAA/3////+//////////7//////////v/////////+//38AAP///7////9/////v/////////+//38AAP///78AAP9/////v/////////+//////////7//////////v/////////+//////////7//////////v/////////+//////////7//////////v/////////+//////////7//////////v/////////+//////////78=")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_gbe52")

[node name="RugRectangle" type="MeshInstance3D"]
transform = Transform3D(1, 0, 0, 0, 0, -1, 0, 1, 0, 0, 0, 0)
mesh = SubResource("ArrayMesh_mflhh")
skeleton = NodePath("")
