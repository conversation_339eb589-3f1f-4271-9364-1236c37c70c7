[gd_scene load_steps=4 format=3 uid="uid://bebbenbf5vuiw"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_kp7rd"]
[ext_resource type="PackedScene" uid="uid://c0mjh2ruit2wc" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/MicroWave.tscn" id="2_c2drv"]

[sub_resource type="BoxShape3D" id="BoxShape3D_kwcmx"]
size = Vector3(2.03394, 2.1, 2.40076)

[node name="MacrowavetProp" instance=ExtResource("1_kp7rd")]

[node name="MicroWave" parent="Meshes" index="0" instance=ExtResource("2_c2drv")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00378418, 1.01726, 0.172546)
shape = SubResource("BoxShape3D_kwcmx")
