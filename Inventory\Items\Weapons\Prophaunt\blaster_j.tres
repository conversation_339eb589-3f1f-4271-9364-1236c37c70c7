[gd_resource type="Resource" script_class="GunInventoryItem" load_steps=4 format=3 uid="uid://gus3p8t0a1mq3"]

[ext_resource type="Texture2D" uid="uid://pm05jfwruxpr" path="res://Scenes/ui/assets/pistol.png" id="1_blaster_j_icon"]
[ext_resource type="PackedScene" uid="uid://tnyaj64filso" path="res://prophaunt/assets/Guns/Scene/blaster_j.tscn" id="2_blaster_j_scene"]
[ext_resource type="Script" path="res://Inventory/GunInventoryItem.gd" id="3_blaster_j_script"]

[resource]
script = ExtResource("3_blaster_j_script")
damage = 100
ammo = 20
gun_type = "range"
range = 48
animation_speed = 2.7
scene = ExtResource("2_blaster_j_scene")
scale = Vector3(150, 150, 150)
position = Vector3(0, 0, 0)
rotation = Vector3(10, 125, 90)
ui = 2
id = 106
name = "BLASTER_J"
icon = ExtResource("1_blaster_j_icon")
price = 320
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
