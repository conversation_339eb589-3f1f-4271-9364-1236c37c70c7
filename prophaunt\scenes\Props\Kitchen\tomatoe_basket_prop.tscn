[gd_scene load_steps=4 format=3 uid="uid://qgayq3vcobtx"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_mo5k1"]
[ext_resource type="PackedScene" uid="uid://c73bhoj5ie4o8" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/BasketTomatoes.tscn" id="2_funng"]

[sub_resource type="BoxShape3D" id="BoxShape3D_avaa3"]
size = Vector3(2.06488, 1.02612, 2.0271)

[node name="TomatoeBascketProp" instance=ExtResource("1_mo5k1")]

[node name="BasketTomatoes" parent="Meshes" index="0" instance=ExtResource("2_funng")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00650024, 0.468872, 0.00622559)
shape = SubResource("BoxShape3D_avaa3")
