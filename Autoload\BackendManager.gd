extends Node

signal register_complete

var UPDATE_STATE_TIME = 3
var start_game_http
var finish_game_http
var register_http
var update_state_http
var add_coin_http
var start_minigame_http
var end_mench_http
var registered = false
var logs = false
var map_id = 0
var server_key = ""

var retry_counter = 0
var MAX_RETRY = 2
var server_is_test = false

func _ready():
	register_http = HTTPRequest.new()
	register_http.request_completed.connect(on_register_complete)
	add_child(register_http)
	update_state_http = HTTPRequest.new()
	update_state_http.request_completed.connect(on_update_state_complete)
	add_child(update_state_http)
	start_game_http = HTTPRequest.new()
	start_game_http.request_completed.connect(on_start_game_complete)
	add_child(start_game_http)
	finish_game_http = HTTPRequest.new()
	finish_game_http.request_completed.connect(on_finish_game_complete)
	add_child(finish_game_http)
	add_coin_http = HTTPRequest.new()
	add_coin_http.request_completed.connect(on_add_coin_finished)
	add_child(add_coin_http)
	start_minigame_http = HTTPRequest.new()
	start_minigame_http.request_completed.connect(on_start_minigame_complete)
	add_child(start_minigame_http)
	end_mench_http = HTTPRequest.new()
	end_mench_http.request_completed.connect(on_end_mench_complete)
	add_child(end_mench_http)


var state_time_counter = 0
func _process(delta):
	if Constants.is_client():
		set_process(false)
		return
	
	if not add_coin_requesting:
		pop_add_coin_q()
	
	
	state_time_counter += delta
	if state_time_counter >= UPDATE_STATE_TIME:
		if registered:
			state_time_counter = 0
			send_update_state_request()


func send_register_request():
	var url = Constants.BACKEND_URL + "/game/register/"
	var data = {
		"ip": Constants.SERVER_IP,
		"port": Constants.SERVER_PORT,
	}

	if Constants.REGISTER_PORT != -1:
		data["port"] = Constants.REGISTER_PORT

	if Constants.game_mode == Constants.GameMode.Race:
		data["mode"] = "Race"
	elif Constants.game_mode == Constants.GameMode.FreeRide:
		data["mode"] = "FreeRide"
	else:
		data["mode"] = "PropHaunt"
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + Constants.BACKEND_TOKEN]
	register_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_register_complete(_result, response_code, _headers, body):
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		registered = true
		Constants.BACKEND_ID = json["id"]
		server_is_test = json["is_test"]
		server_key = json["server_key"]
		StageManager.on_start(json["total_stage"])
		print("**Registered to Backend Successfuly (id=", Constants.BACKEND_ID, ")**: ", server_key)
		emit_signal("register_complete")
	else:
		print("register failed")
		if Constants.game_mode == Constants.GameMode.Race:
			Constants.kill_server()
			return
		await get_tree().create_timer(2).timeout
		send_register_request()


func send_update_state_request():
	if Constants.server == null:
		return
	var url = Constants.BACKEND_URL + "/game/update/"
	var players_joined = 0
	var bot_count = 0
	var state = Constants.ServerStateStrings[0] #Loading
	if Constants.server != null:
		players_joined = Constants.server.players_count
		state = Constants.ServerStateStrings[Constants.server.state]
		bot_count = Constants.server.count_bot_players()

	if bot_count > 0 and players_joined == 0:
		bot_count = 0
		players_joined = 0

	var data = {
		"id": Constants.BACKEND_ID,
		"state": state,
		"players_joined": players_joined,
		"map_id": map_id,
		"bot_count": bot_count,
	}
	if Constants.game_mode == Constants.GameMode.Race:
		data["mode"] = "Race"
	elif Constants.game_mode == Constants.GameMode.FreeRide:
		data["mode"] = "FreeRide"
	else:
		data["mode"] = "PropHaunt"
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + Constants.BACKEND_TOKEN]
	update_state_http.cancel_request()
	update_state_http.request(url, headers, HTTPClient.METHOD_POST, json)


var update_error_max = 20
var update_error_count = 0
func on_update_state_complete(_result, response_code, _headers, body):
	if response_code == 200:
		update_error_count = 0
		var json = JSON.parse_string(body.get_string_from_utf8())
		Constants.SERVER_FORCE_NEW_PLAYER = json["force_new_player"]
		Constants.SERVER_FORCE_RECONNECT_PLAYER = json["force_reconnect"]
		Constants.server.MAX_PLAYERS = json["max_players_count"]
		Constants.SERVER_GUN_ALLOW = json["gun_allow"]
		VehicleManager.MAX_VEHICLE = json["max_vehicle"]
		if logs:
			print("state updated with backend")
	else:
		update_error_count += 1
		if update_error_count >= update_error_max:
			print("TOO MUCH ERROR! Exiting")
			registered = false
			
			if Constants.server:
				if Constants.server.peer:
					Constants.server.peer.close()
			
			StageManager.is_finished = true
			Constants.kill_server()


var last_players_data
func send_start_game_request(players_data):
	last_players_data = players_data
	var url = Constants.BACKEND_URL + "/game/start_game/"
	var ids = []
	for key in players_data:
		if players_data[key]["server"]["eliminated"] == true:
			continue

		var packet = {
			"handle": players_data[key]["selection"]["name"],
			"id": players_data[key]["backend_id"],
			"character_id": players_data[key]["selection"]["character_id"]
		}
		ids.append(packet)
	var data = {
		"ids": ids,
		"game_server_id": Constants.BACKEND_ID,
		"player_count": ids.size(),
	}
	print("-sending start game request: Starting Player Count=", data["player_count"])
	GameSettings.current_stage_players_count = data["player_count"]
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + Constants.BACKEND_TOKEN]
	start_game_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_start_game_complete(_result, response_code, _headers, body):
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		GameSettings.backend_game_id = json["game_id"]
		GameSettings.backend_container_id = json["container_id"]
		GameSettings.qualified_game_finished = json["qualified_count"]
		print("Qualified count=", GameSettings.qualified_game_finished)
		if logs:
			print("start game sync with backend")
		return
	
	
	print("ERROR on start game request: ", response_code)
	if retry_counter < MAX_RETRY:
		retry_counter += 1
		await get_tree().create_timer(0.5).timeout
		send_start_game_request(last_players_data)


var last_map_type
func send_finish_game_request(map_type):
	var url = Constants.BACKEND_URL + "/game/finish_game/"
	var ids = []
	var rank = 1
	var metas = GameSettings.players_meta_data.duplicate(true)
	last_map_type = map_type
	
	for value in GameSettings.qualified_players_data:
		if map_type == Constants.MapType.Elimination:
			rank = len(GameSettings.qualified_players_data)
		var result = {
			"id": value["backend_id"],
			"rank": rank,
			"time": value["time"],
			"character_id": value["selection"]["character_id"]
		}
		if metas.has(value["id"]):
			var helper = metas[value["id"]]
			result["fps"] = helper["fps"]
			result["fps_count"] = helper["fps_count"]
			result["ping"] = helper["ping"]
			result["ping_count"] = helper["ping_count"]
			metas.erase(value["id"])

		ids.append(result)
		rank += 1
	
	#Add remaining meta datas
	for key in metas.keys():
		if map_type == Constants.MapType.Elimination:
			continue
		var helper = metas[key]
		if helper["backend_id"] == null:
			continue

		var result = {
			"id": helper["backend_id"],
			"rank": -1,
			"time": -1,
			"fps": helper["fps"],
			"fps_count": helper["fps_count"],
			"ping": helper["ping"],
			"ping_count": helper["ping_count"]
		}
		ids.append(result)

	var data = {
		"ids": ids,
		"game_id": GameSettings.backend_game_id
	}
	print("-sending finish game request")
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + Constants.BACKEND_TOKEN]
	finish_game_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_finish_game_complete(_result, response_code, _headers, _body):
	if response_code == 200:
		if logs:
			print("finish game sync with backend")
		return
	
	print("Finish request failed: ", response_code)
	
	if retry_counter < MAX_RETRY:
		retry_counter += 1
		await get_tree().create_timer(0.5).timeout
		send_finish_game_request(last_map_type)
	else:
		Constants.kill_server()


var add_coin_q = []
var add_coin_requesting = false
func queue_freeride_add_coin_request(player_key, coin, type="general", secondary=""):
	add_coin_q.append({
		"server_key": server_key,
		"player_backend_id": Constants.server.players_data[player_key]["backend_id"],
		"value": coin,
		"type": type,
		"secondary": secondary,
		"player_key": player_key,
	})


func pop_add_coin_q():
	if add_coin_q.size() > 0:
		send_freeride_add_coin_request(add_coin_q.pop_front())


func send_freeride_add_coin_request(data):
	print("sending add coin: ", data)
	add_coin_requesting = true
	var url = Constants.BACKEND_URL + "/game/freeride_add_coin/"

	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + Constants.BACKEND_TOKEN]
	add_coin_http.cancel_request()
	add_coin_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_add_coin_finished(_result, response_code, _headers, body):
	add_coin_requesting = false
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		if json.has("player_key"):
			ClientRPC.update_stats.rpc_id(int(json["player_key"]))
		print("Add Coin Success")
	else:
		print("Add Coin Failed! ", response_code)
		var json = JSON.parse_string(body.get_string_from_utf8())
		print(json)


func send_start_minigame_request(data):
	var url = Constants.BACKEND_URL + "/minigame/start_minigame/"
	data["server_key"] = server_key
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + Constants.BACKEND_TOKEN]
	start_minigame_http.cancel_request()
	start_minigame_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_start_minigame_complete(_result, response_code, _headers, body):
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		var minigames = get_tree().get_nodes_in_group("MiniGame")
		for game in minigames:
			if game.id == json["godot_id"]:
				game.minigame_backend_id = json["minigame_id"]
	else:
		print(response_code)


func send_end_minigame_request(data):
	var url = Constants.BACKEND_URL + "/minigame/end_minigame/"
	data["server_key"] = server_key
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + Constants.BACKEND_TOKEN]
	end_mench_http.cancel_request()
	end_mench_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_end_mench_complete(_result, response_code, _headers, body):
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		print(json)
	else:
		print(response_code)
