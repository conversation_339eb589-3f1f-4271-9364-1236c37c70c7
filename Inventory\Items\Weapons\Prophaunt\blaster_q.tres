[gd_resource type="Resource" script_class="GunInventoryItem" load_steps=4 format=3 uid="uid://jxv6s1w3d4pt6"]

[ext_resource type="Texture2D" uid="uid://pm05jfwruxpr" path="res://Scenes/ui/assets/pistol.png" id="1_blaster_q_icon"]
[ext_resource type="PackedScene" uid="uid://0qd1ekc6wl0l" path="res://prophaunt/assets/Guns/Scene/blaster_q.tscn" id="2_blaster_q_scene"]
[ext_resource type="Script" path="res://Inventory/GunInventoryItem.gd" id="3_blaster_q_script"]

[resource]
script = ExtResource("3_blaster_q_script")
damage = 75
ammo = 28
gun_type = "range"
range = 30
animation_speed = 4.0
scene = ExtResource("2_blaster_q_scene")
scale = Vector3(150, 150, 150)
position = Vector3(0, 0, 0)
rotation = Vector3(10, 125, 90)
ui = 2
id = 109
name = "BLASTER_Q"
icon = ExtResource("1_blaster_q_icon")
price = 200
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
