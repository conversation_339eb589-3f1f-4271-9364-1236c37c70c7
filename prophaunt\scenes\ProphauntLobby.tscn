[gd_scene load_steps=12 format=3 uid="uid://b1au20ihj4qs6"]

[ext_resource type="Script" path="res://prophaunt/scripts/ProphauntLobbyScene.gd" id="1_lobby"]
[ext_resource type="PackedScene" uid="uid://c0bvc7tlsk7xo" path="res://Scenes/ui/character_preview.tscn" id="2_char_preview"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="3_custom_button"]
[ext_resource type="PackedScene" uid="uid://dfw3vd3t4jtva" path="res://Scenes/ui/coin_show.tscn" id="4_coin_show"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="5_font"]
[ext_resource type="Texture2D" uid="uid://cfkrhs0n66fyu" path="res://Scenes/ui/assets/button_light.png" id="6_button_bg"]
[ext_resource type="Texture2D" uid="uid://r7n4eqwqtiue" path="res://Scenes/ui/assets/gradiant_bg.png" id="7_bg"]
[ext_resource type="PackedScene" uid="uid://b3elqmgq54s52" path="res://Scenes/ui/loading_element.tscn" id="8_loading"]
[ext_resource type="Script" path="res://Scenes/CharacterPreviewImage.gd" id="8_qbsso"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_bg"]
texture = ExtResource("7_bg")

[sub_resource type="ViewportTexture" id="ViewportTexture_vfqyo"]
viewport_path = NodePath(".")

[node name="ProphauntLobby" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_lobby")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_bg")

[node name="CharacterPreviewImage" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 5.0
offset_top = -68.0
offset_right = 5.0
offset_bottom = -218.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("ViewportTexture_vfqyo")
expand_mode = 2
stretch_mode = 5
script = ExtResource("8_qbsso")
metadata/_edit_lock_ = true

[node name="LoadingElement" parent="CharacterPreviewImage" instance=ExtResource("8_loading")]
visible = false
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -19.9999
offset_top = 69.0
offset_right = 20.0001
offset_bottom = 109.0

[node name="TopPanel" type="Control" parent="."]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 100.0
grow_horizontal = 2

[node name="BackButton" parent="TopPanel" instance=ExtResource("3_custom_button")]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_right = 0.0
anchor_bottom = 0.5
offset_left = 20.0
offset_top = -40.0
offset_right = 120.0
offset_bottom = 40.0
grow_horizontal = 1
pivot_offset = Vector2(50, 40)

[node name="BackButtonBG" type="TextureRect" parent="TopPanel/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_button_bg")
expand_mode = 1
stretch_mode = 4

[node name="BackButtonLabel" type="Label" parent="TopPanel/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 24
text = "BACK"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CurrencyPanel" type="Control" parent="TopPanel"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -320.0
offset_bottom = 100.0
grow_horizontal = 0

[node name="CoinShow" parent="TopPanel/CurrencyPanel" instance=ExtResource("4_coin_show")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -150.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 70.0
grow_horizontal = 0
grow_vertical = 1
link_to_shop = false

[node name="SmartShow" type="Control" parent="TopPanel/CurrencyPanel"]
custom_minimum_size = Vector2(120, 50)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -280.0
offset_top = 20.0
offset_right = -160.0
offset_bottom = 70.0
grow_horizontal = 0

[node name="SmartBG" type="NinePatchRect" parent="TopPanel/CurrencyPanel/SmartShow"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("6_button_bg")
patch_margin_left = 33
patch_margin_top = 17
patch_margin_right = 25
patch_margin_bottom = 21

[node name="SmartLabel" type="Label" parent="TopPanel/CurrencyPanel/SmartShow"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 1
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 20
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CharacterContainer" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="CharacterPreview" parent="CharacterContainer" instance=ExtResource("2_char_preview")]
transparent_bg = true

[node name="WeaponPanel" type="Control" parent="."]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 50.0
offset_top = -100.0
offset_right = 200.0
offset_bottom = 100.0
grow_vertical = 2

[node name="WeaponContainer" type="VBoxContainer" parent="WeaponPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="WeaponTitle" type="Label" parent="WeaponPanel/WeaponContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 24
text = "WEAPON"
horizontal_alignment = 1

[node name="WeaponIcon" type="TextureRect" parent="WeaponPanel/WeaponContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
size_flags_horizontal = 4
expand_mode = 1
stretch_mode = 4

[node name="AbilitiesPanel" type="Control" parent="."]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -100.0
offset_right = -50.0
offset_bottom = 100.0
grow_horizontal = 0
grow_vertical = 2

[node name="AbilitiesContainer" type="VBoxContainer" parent="AbilitiesPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="AbilitiesTitle" type="Label" parent="AbilitiesPanel/AbilitiesContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 24
text = "ABILITIES"
horizontal_alignment = 1

[node name="GrenadesContainer" type="HBoxContainer" parent="AbilitiesPanel/AbilitiesContainer"]
layout_mode = 2

[node name="GrenadesLabel" type="Label" parent="AbilitiesPanel/AbilitiesContainer/GrenadesContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 1
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 18
text = "GRENADES:"

[node name="GrenadesCount" type="Label" parent="AbilitiesPanel/AbilitiesContainer/GrenadesContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 1
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 18
text = "0"

[node name="HexesContainer" type="HBoxContainer" parent="AbilitiesPanel/AbilitiesContainer"]
layout_mode = 2

[node name="HexesLabel" type="Label" parent="AbilitiesPanel/AbilitiesContainer/HexesContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 1
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 18
text = "HEXES:"

[node name="HexesCount" type="Label" parent="AbilitiesPanel/AbilitiesContainer/HexesContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 1
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 18
text = "0"

[node name="BottomPanel" type="Control" parent="."]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -100.0
grow_horizontal = 2
grow_vertical = 0

[node name="StartButton" parent="BottomPanel" instance=ExtResource("3_custom_button")]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
offset_left = -150.0
offset_top = -80.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 0
grow_vertical = 0
pivot_offset = Vector2(65, 30)

[node name="StartButtonBG" type="TextureRect" parent="BottomPanel/StartButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_button_bg")
expand_mode = 1
stretch_mode = 4

[node name="StartButtonLabel" type="Label" parent="BottomPanel/StartButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 24
text = "START"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LoadingPanel" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="LoadingBG" type="Panel" parent="LoadingPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_bg")

[node name="LoadingElement" parent="LoadingPanel" instance=ExtResource("8_loading")]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0

[node name="LoadingLabel" type="Label" parent="LoadingPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = 30.0
offset_right = 100.0
offset_bottom = 80.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 24
text = "LOADING"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HTTPRequest" type="HTTPRequest" parent="."]

[connection signal="gui_input" from="CharacterPreviewImage" to="CharacterPreviewImage" method="_on_full_screen_gui_input"]
[connection signal="pressed" from="TopPanel/BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="BottomPanel/StartButton" to="." method="_on_start_button_pressed"]
[connection signal="request_completed" from="HTTPRequest" to="." method="_on_http_request_completed"]
